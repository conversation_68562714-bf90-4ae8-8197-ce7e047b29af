@using DiagramEditor.Services
@inject IThemeService ThemeService

<MudMenu Icon="@Icons.Material.Filled.Palette" 
         Color="Color.Inherit" 
         Direction="Direction.Bottom" 
         OffsetX="true">
    <ActivatorContent>
        <MudTooltip Text="Selecionar Tema">
            <MudIconButton Icon="@Icons.Material.Filled.Palette" 
                          Color="Color.Inherit" />
        </MudTooltip>
    </ActivatorContent>
    <ChildContent>
        <MudContainer Class="pa-4" Style="min-width: 300px;">
            <MudStack Spacing="3">
                <!-- Título -->
                <MudText Typo="Typo.h6" Class="mb-2">
                    <MudIcon Icon="@Icons.Material.Filled.Palette" Class="mr-2" />
                    Personalizar Tema
                </MudText>

                <!-- Modo Claro/Escuro -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-2">Modo</MudText>
                        <MudToggleGroup T="ThemeMode" 
                                       Value="@ThemeService.CurrentMode" 
                                       ValueChanged="OnModeChanged"
                                       MultiSelection="false"
                                       FixedContent="true">
                            <MudToggleItem Value="ThemeMode.Light">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.LightMode" Size="Size.Small" />
                                    <MudText>Claro</MudText>
                                </MudStack>
                            </MudToggleItem>
                            <MudToggleItem Value="ThemeMode.Dark">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudIcon Icon="@Icons.Material.Filled.DarkMode" Size="Size.Small" />
                                    <MudText>Escuro</MudText>
                                </MudStack>
                            </MudToggleItem>
                        </MudToggleGroup>
                    </MudCardContent>
                </MudCard>

                <!-- Presets de Temas -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-3">Temas Predefinidos</MudText>
                        <MudGrid>
                            @foreach (var preset in GetThemePresets())
                            {
                                <MudItem xs="6">
                                    <MudButton Variant="@(IsCurrentPreset(preset.Value) ? Variant.Filled : Variant.Outlined)"
                                             Color="@preset.Color"
                                             FullWidth="true"
                                             StartIcon="@preset.Icon"
                                             OnClick="() => OnPresetSelected(preset.Value)"
                                             Class="theme-preset-button">
                                        @preset.Name
                                    </MudButton>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCardContent>
                </MudCard>

                <!-- Personalização Avançada -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-2">Personalização</MudText>
                        <MudStack Spacing="2">
                            <MudColorPicker @bind-Value="CustomPrimaryColor" 
                                          Label="Cor Primária"
                                          OnColorChanged="OnCustomColorChanged" />
                            
                            <MudColorPicker @bind-Value="CustomSecondaryColor" 
                                          Label="Cor Secundária"
                                          OnColorChanged="OnCustomColorChanged" />
                            
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Primary" 
                                     StartIcon="@Icons.Material.Filled.Refresh"
                                     OnClick="ResetToDefault"
                                     FullWidth="true">
                                Restaurar Padrão
                            </MudButton>
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Prévia do Tema -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-2">Prévia</MudText>
                        <MudStack Spacing="1">
                            <MudChip Color="Color.Primary" Size="Size.Small">Primária</MudChip>
                            <MudChip Color="Color.Secondary" Size="Size.Small">Secundária</MudChip>
                            <MudChip Color="Color.Success" Size="Size.Small">Sucesso</MudChip>
                            <MudChip Color="Color.Warning" Size="Size.Small">Aviso</MudChip>
                            <MudChip Color="Color.Error" Size="Size.Small">Erro</MudChip>
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Ações -->
                <MudStack Row Spacing="2">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Save"
                             OnClick="SaveTheme"
                             FullWidth="true">
                        Salvar
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             StartIcon="@Icons.Material.Filled.Share"
                             OnClick="ShareTheme"
                             FullWidth="true">
                        Compartilhar
                    </MudButton>
                </MudStack>
            </MudStack>
        </MudContainer>
    </ChildContent>
</MudMenu>

<style>
    .theme-preset-button {
        height: 48px;
        transition: all 0.3s ease;
    }
    
    .theme-preset-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>

@code {
    private MudBlazor.Utilities.MudColor CustomPrimaryColor = "#2196F3";
    private MudBlazor.Utilities.MudColor CustomSecondaryColor = "#FF5722";
    private ThemePreset _currentPreset = ThemePreset.Light;

    protected override void OnInitialized()
    {
        ThemeService.ThemeChanged += StateHasChanged;
        LoadCurrentColors();
    }

    private void LoadCurrentColors()
    {
        CustomPrimaryColor = ThemeService.CurrentTheme.Palette.Primary;
        CustomSecondaryColor = ThemeService.CurrentTheme.Palette.Secondary;
    }

    private void OnModeChanged(ThemeMode mode)
    {
        ThemeService.SetMode(mode);
    }

    private void OnPresetSelected(ThemePreset preset)
    {
        _currentPreset = preset;
        ThemeService.SetTheme(preset);
        LoadCurrentColors();
    }

    private bool IsCurrentPreset(ThemePreset preset)
    {
        return _currentPreset == preset;
    }

    private void OnCustomColorChanged()
    {
        // Criar tema personalizado com as cores selecionadas
        var customTheme = new MudTheme()
        {
            Palette = ThemeService.CurrentMode == ThemeMode.Dark ? 
                new PaletteDark()
                {
                    Primary = CustomPrimaryColor.ToString(),
                    Secondary = CustomSecondaryColor.ToString(),
                    Background = "#121212",
                    Surface = "#1E1E1E",
                    TextPrimary = "#FFFFFF"
                } :
                new PaletteLight()
                {
                    Primary = CustomPrimaryColor.ToString(),
                    Secondary = CustomSecondaryColor.ToString(),
                    Background = "#FFFFFF",
                    Surface = "#F5F5F5",
                    TextPrimary = "#212121"
                }
        };

        var customDiagramTheme = new DiagramTheme
        {
            CanvasBackground = ThemeService.CurrentMode == ThemeMode.Dark ? "#1E1E1E" : "#FFFFFF",
            GridColor = ThemeService.CurrentMode == ThemeMode.Dark ? "#404040" : "#E0E0E0",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = CustomPrimaryColor.ToString(),
                BorderColor = CustomSecondaryColor.ToString(),
                TextColor = ThemeService.CurrentMode == ThemeMode.Dark ? "#000000" : "#FFFFFF"
            }
        };

        ThemeService.SetCustomTheme(customTheme, customDiagramTheme);
    }

    private void ResetToDefault()
    {
        OnPresetSelected(ThemePreset.Light);
    }

    private async Task SaveTheme()
    {
        await ThemeService.SaveThemePreferenceAsync();
        // Mostrar notificação de sucesso
    }

    private void ShareTheme()
    {
        // Implementar compartilhamento de tema
        // Gerar código do tema para compartilhar
    }

    private List<ThemePresetInfo> GetThemePresets()
    {
        return new List<ThemePresetInfo>
        {
            new(ThemePreset.Light, "Claro", Icons.Material.Filled.LightMode, Color.Default),
            new(ThemePreset.Dark, "Escuro", Icons.Material.Filled.DarkMode, Color.Dark),
            new(ThemePreset.Blue, "Azul", Icons.Material.Filled.Water, Color.Primary),
            new(ThemePreset.Green, "Verde", Icons.Material.Filled.Eco, Color.Success),
            new(ThemePreset.Purple, "Roxo", Icons.Material.Filled.AutoAwesome, Color.Secondary),
            new(ThemePreset.HighContrast, "Alto Contraste", Icons.Material.Filled.Contrast, Color.Warning)
        };
    }

    public void Dispose()
    {
        ThemeService.ThemeChanged -= StateHasChanged;
    }

    private record ThemePresetInfo(ThemePreset Value, string Name, string Icon, Color Color);
}
