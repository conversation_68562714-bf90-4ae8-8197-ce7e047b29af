using NanoidDotNet;

namespace Blazor.Diagrams.Core;

public static class Helpers
{
    /// <summary>
    /// Generate nanoid using 18 characters with uppercase letters and digits where, if adding 1000 IDs per second,
    /// it will be ~456 years or 14T IDs needed, to have a 1% probability of at least one collision.
    /// </summary>
    /// <param name="size">The length of the id. Defaults to 17.</param>
    /// <returns>A new string representing a random nanoid with the specified alphabet and size.</returns>
    public static string NanoidGenerator(int size = 18) => Nanoid.Generate(Nanoid.Alphabets.UppercaseLettersAndDigits, size);
}
