@page "/diagram-editor"
@page "/diagram-editor/{DiagramId}"
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Geometry
@using Blazor.Diagrams.Core.PathGenerators
@using Blazor.Diagrams.Core.Routers
@using Blazor.Diagrams.Core.Anchors
@using Blazor.Diagrams.Core.Persistence
@using Blazor.Diagrams.Options
@using global::DiagramEditor.Components
@using global::DiagramEditor.Models
@using static global::DiagramEditor.Components.DiagramToolbar

@inject global::DiagramEditor.Services.IDiagramRepository DiagramRepository
@inject global::DiagramEditor.Services.DiagramService DiagramService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Editor de Diagramas</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-0" Style="height: calc(100vh - 120px);">
    <!-- Toolbar Avançado -->
    <DiagramToolbar DiagramName="@diagramName"
                   DiagramNameChanged="@((name) => diagramName = name)"
                   ZoomLevel="@diagram.Zoom"
                   OnSave="SaveDiagram"
                   OnLoad="LoadDiagram"
                   OnClear="ClearDiagram"
                   OnAddNode="AddNodeByType"
                   OnZoomIn="ZoomIn"
                   OnZoomOut="ZoomOut"
                   OnResetView="ResetView"
                   OnSelectAll="SelectAll"
                   OnDeleteSelected="DeleteSelected"
                   OnExportImage="ExportImage"
                   OnExportPdf="ExportPdf"
                   OnExportJson="ExportJson" />

    <!-- Canvas do Diagrama -->
    <MudPaper Elevation="2" Style="height: calc(100% - 120px); position: relative; overflow: hidden;">
        <CascadingValue Value="diagram" IsFixed="true">
            <DiagramCanvas />
        </CascadingValue>
    </MudPaper>

    <!-- Painel de Propriedades -->
    <NodePropertiesPanel @bind-IsOpen="propertiesPanelOpen"
                        SelectedNode="selectedNode"
                        OnNodeUpdated="RefreshDiagram"
                        OnNodeDeleted="DeleteSelectedNode" />
</MudContainer>

@code {
    [Parameter] public string? DiagramId { get; set; }
    
    private BlazorDiagram diagram = null!;
    private string diagramName = "Novo Diagrama";
    private string? currentDiagramId;
    private bool propertiesPanelOpen = false;
    private NodeModel? selectedNode = null;
    private int nodeCounter = 1;

    protected override async Task OnInitializedAsync()
    {
        // Configurar opções do diagrama
        var options = new BlazorDiagramOptions();

        diagram = new BlazorDiagram(options);

        // Registrar componentes personalizados será implementado posteriormente

        // Configurar eventos
        diagram.SelectionChanged += OnSelectionChanged;

        // Carregar diagrama se ID foi fornecido
        if (!string.IsNullOrEmpty(DiagramId))
        {
            await LoadDiagramById(DiagramId);
        }
    }

    private async Task SaveDiagram()
    {
        try
        {
            var diagramData = new global::DiagramEditor.Services.DiagramData
            {
                Id = currentDiagramId ?? Helpers.NanoidGenerator(),
                Name = diagramName,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Data = SerializeDiagram()
            };

            if (currentDiagramId == null)
            {
                await DiagramRepository.CreateAsync(diagramData);
                currentDiagramId = diagramData.Id;
            }
            else
            {
                diagramData.UpdatedAt = DateTime.UtcNow;
                await DiagramRepository.UpdateAsync(diagramData);
            }

            Snackbar.Add("Diagrama salvo com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDiagram()
    {
        // Implementar dialog para selecionar diagrama
        Navigation.NavigateTo("/saved-diagrams");
    }

    private async Task LoadDiagramById(string id)
    {
        try
        {
            var diagramData = await DiagramRepository.GetByIdAsync(id);
            if (diagramData != null)
            {
                currentDiagramId = diagramData.Id;
                diagramName = diagramData.Name;
                DeserializeDiagram(diagramData.Data);
                Snackbar.Add("Diagrama carregado com sucesso!", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private void ClearDiagram()
    {
        diagram.Nodes.Clear();
        diagram.Links.Clear();
        diagram.Refresh();
        currentDiagramId = null;
        diagramName = "Novo Diagrama";
    }

    private void AddNodeByType(NodeType nodeType)
    {
        var position = new Point(100 + diagram.Nodes.Count * 150, 100 + diagram.Nodes.Count * 50);
        NodeModel node = nodeType switch
        {
            NodeType.Rectangle => new RectangleNodeModel(position) { Title = $"Retângulo {nodeCounter++}" },
            NodeType.Circle => new CircleNodeModel(position) { Title = $"Círculo {nodeCounter++}" },
            NodeType.Triangle => new TriangleNodeModel(position) { Title = $"Triângulo {nodeCounter++}" },
            NodeType.Diamond => new DiamondNodeModel(position) { Title = $"Losango {nodeCounter++}" },
            _ => new NodeModel(position) { Title = $"Nó {nodeCounter++}" }
        };

        diagram.Nodes.Add(node);
    }

    private void OnSelectionChanged()
    {
        selectedNode = diagram.GetSelectedModels().OfType<NodeModel>().FirstOrDefault();
        if (selectedNode != null)
        {
            propertiesPanelOpen = true;
        }
        StateHasChanged();
    }

    private void ZoomIn()
    {
        diagram.SetZoom(diagram.Zoom * 1.1);
    }

    private void ZoomOut()
    {
        diagram.SetZoom(diagram.Zoom * 0.9);
    }

    private void ResetView()
    {
        diagram.SetZoom(1.0);
        diagram.SetPan(0, 0);
    }

    private void SelectAll()
    {
        foreach (var node in diagram.Nodes)
        {
            node.Selected = true;
        }
        diagram.Refresh();
    }

    private void DeleteSelected()
    {
        var selectedNodes = diagram.GetSelectedModels().OfType<NodeModel>().ToList();
        var selectedLinks = diagram.GetSelectedModels().OfType<LinkModel>().ToList();

        foreach (var node in selectedNodes)
        {
            diagram.Nodes.Remove(node);
        }

        foreach (var link in selectedLinks)
        {
            diagram.Links.Remove(link);
        }

        diagram.Refresh();
    }

    private void DeleteSelectedNode()
    {
        if (selectedNode != null)
        {
            diagram.Nodes.Remove(selectedNode);
            selectedNode = null;
            propertiesPanelOpen = false;
            diagram.Refresh();
        }
    }

    private void RefreshDiagram()
    {
        diagram.Refresh();
        StateHasChanged();
    }

    private string SerializeDiagram()
    {
        return DiagramService.SerializeDiagram(diagram);
    }

    private void DeserializeDiagram(string jsonData)
    {
        try
        {
            DiagramService.DeserializeDiagram(diagram, jsonData);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao deserializar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportImage()
    {
        try
        {
            // Implementar exportação para PNG usando JavaScript
            await JSRuntime.InvokeVoidAsync("exportDiagramAsPNG", "diagram-canvas");
            Snackbar.Add("Diagrama exportado como PNG!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao exportar imagem: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportPdf()
    {
        try
        {
            // Implementar exportação para PDF usando JavaScript
            await JSRuntime.InvokeVoidAsync("exportDiagramAsPDF", "diagram-canvas");
            Snackbar.Add("Diagrama exportado como PDF!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao exportar PDF: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExportJson()
    {
        try
        {
            var jsonData = SerializeDiagram();
            var fileName = $"{diagramName}_{DateTime.Now:yyyyMMdd_HHmmss}.json";

            // Implementar download do JSON usando JavaScript
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, jsonData, "application/json");
            Snackbar.Add("Diagrama exportado como JSON!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao exportar JSON: {ex.Message}", Severity.Error);
        }
    }
}
