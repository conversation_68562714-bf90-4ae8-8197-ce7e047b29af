@page "/diagram-editor"
@page "/diagram-editor/{DiagramId}"
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Geometry
@using Blazor.Diagrams.Core.PathGenerators
@using Blazor.Diagrams.Core.Routers
@using Blazor.Diagrams.Core.Anchors
@using Blazor.Diagrams.Core.Persistence
@using Blazor.Diagrams.Options

@inject global::DiagramEditor.Services.IDiagramRepository DiagramRepository
@inject global::DiagramEditor.Services.DiagramService DiagramService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Editor de Diagramas</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-0" Style="height: calc(100vh - 120px);">
    <MudPaper Elevation="1" Class="pa-2 mb-2">
        <MudStack Row Spacing="2" AlignItems="MudBlazor.AlignItems.Center">
            <MudTextField @bind-Value="diagramName" Label="Nome do Diagrama" Variant="Variant.Outlined" />
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveDiagram" StartIcon="@Icons.Material.Filled.Save">
                Salvar
            </MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Secondary" OnClick="LoadDiagram" StartIcon="@Icons.Material.Filled.FolderOpen">
                Carregar
            </MudButton>
            <MudButton Variant="Variant.Outlined" Color="Color.Error" OnClick="ClearDiagram" StartIcon="@Icons.Material.Filled.Clear">
                Limpar
            </MudButton>
            <MudSpacer />
            <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="AddNode" StartIcon="@Icons.Material.Filled.Add">
                Adicionar Nó
            </MudButton>
        </MudStack>
    </MudPaper>

    <MudPaper Elevation="2" Style="height: calc(100% - 80px); position: relative;">
        <CascadingValue Value="diagram" IsFixed="true">
            <DiagramCanvas />
        </CascadingValue>
    </MudPaper>
</MudContainer>

@code {
    [Parameter] public string? DiagramId { get; set; }
    
    private BlazorDiagram diagram = null!;
    private string diagramName = "Novo Diagrama";
    private string? currentDiagramId;

    protected override async Task OnInitializedAsync()
    {
        // Configurar opções do diagrama
        var options = new BlazorDiagramOptions();

        diagram = new BlazorDiagram(options);

        // Carregar diagrama se ID foi fornecido
        if (!string.IsNullOrEmpty(DiagramId))
        {
            await LoadDiagramById(DiagramId);
        }
    }

    private async Task SaveDiagram()
    {
        try
        {
            var diagramData = new global::DiagramEditor.Services.DiagramData
            {
                Id = currentDiagramId ?? Helpers.NanoidGenerator(),
                Name = diagramName,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Data = SerializeDiagram()
            };

            if (currentDiagramId == null)
            {
                await DiagramRepository.CreateAsync(diagramData);
                currentDiagramId = diagramData.Id;
            }
            else
            {
                diagramData.UpdatedAt = DateTime.UtcNow;
                await DiagramRepository.UpdateAsync(diagramData);
            }

            Snackbar.Add("Diagrama salvo com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDiagram()
    {
        // Implementar dialog para selecionar diagrama
        Navigation.NavigateTo("/saved-diagrams");
    }

    private async Task LoadDiagramById(string id)
    {
        try
        {
            var diagramData = await DiagramRepository.GetByIdAsync(id);
            if (diagramData != null)
            {
                currentDiagramId = diagramData.Id;
                diagramName = diagramData.Name;
                DeserializeDiagram(diagramData.Data);
                Snackbar.Add("Diagrama carregado com sucesso!", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private void ClearDiagram()
    {
        diagram.Nodes.Clear();
        diagram.Links.Clear();
        diagram.Refresh();
        currentDiagramId = null;
        diagramName = "Novo Diagrama";
    }

    private void AddNode()
    {
        var node = new NodeModel(new Point(100 + diagram.Nodes.Count * 150, 100 + diagram.Nodes.Count * 50))
        {
            Title = $"Nó {diagram.Nodes.Count + 1}"
        };

        // Adicionar portas ao nó
        node.AddPort(PortAlignment.Top);
        node.AddPort(PortAlignment.Bottom);
        node.AddPort(PortAlignment.Left);
        node.AddPort(PortAlignment.Right);

        diagram.Nodes.Add(node);
    }

    private string SerializeDiagram()
    {
        return DiagramService.SerializeDiagram(diagram);
    }

    private void DeserializeDiagram(string jsonData)
    {
        try
        {
            DiagramService.DeserializeDiagram(diagram, jsonData);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao deserializar diagrama: {ex.Message}", Severity.Error);
        }
    }
}
