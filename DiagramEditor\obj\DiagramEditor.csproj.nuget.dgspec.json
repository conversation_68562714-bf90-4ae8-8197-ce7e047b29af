{"format": 1, "restore": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\DiagramEditor.csproj": {}}, "projects": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "projectName": "Z.Blazor.Diagrams.Core", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Nanoid": {"target": "Package", "version": "[3.1.0, )", "versionCentrallyManaged": true}, "SvgPathProperties": {"target": "Package", "version": "[1.1.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Nanoid": {"target": "Package", "version": "[3.1.0, )", "versionCentrallyManaged": true}, "SvgPathProperties": {"target": "Package", "version": "[1.1.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Nanoid": {"target": "Package", "version": "[3.1.0, )", "versionCentrallyManaged": true}, "SvgPathProperties": {"target": "Package", "version": "[1.1.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Nanoid": {"target": "Package", "version": "[3.1.0, )", "versionCentrallyManaged": true}, "SvgPathProperties": {"target": "Package", "version": "[1.1.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj", "projectName": "Z.Blazor.Diagrams.Persistence", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "projectName": "Z.Blazor.Diagrams", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\DiagramEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\DiagramEditor.csproj", "projectName": "DiagramEditor", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\DiagramEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj"}}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj"}}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Persistence\\Blazor.Diagrams.Persistence.csproj"}, "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "MudBlazor": {"target": "Package", "version": "[6.11.2, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "MudBlazor": {"target": "Package", "version": "[6.11.2, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "MudBlazor": {"target": "Package", "version": "[6.11.2, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )", "versionCentrallyManaged": true}, "MudBlazor": {"target": "Package", "version": "[6.11.2, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}