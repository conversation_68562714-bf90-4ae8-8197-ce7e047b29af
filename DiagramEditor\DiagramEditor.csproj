<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="MudBlazor" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Blazor.Diagrams\Blazor.Diagrams.csproj" />
    <ProjectReference Include="..\Blazor.Diagrams.Core\Blazor.Diagrams.Core.csproj" />
    <ProjectReference Include="..\Blazor.Diagrams.Persistence\Blazor.Diagrams.Persistence.csproj" />
  </ItemGroup>

</Project>
