using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models;

namespace DiagramEditor.Models
{
    public class DiagramEntity
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string Name { get; set; }
        
        public string Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        public virtual ICollection<NodeEntity> Nodes { get; set; } = new List<NodeEntity>();
        
        public virtual ICollection<LinkEntity> Links { get; set; } = new List<LinkEntity>();
    }
    
    public class NodeEntity
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string DiagramId { get; set; }
        
        [ForeignKey("DiagramId")]
        public virtual DiagramEntity Diagram { get; set; }
        
        public string Title { get; set; }
        
        public double X { get; set; }
        
        public double Y { get; set; }
        
        public string DataJson { get; set; }
        
        [NotMapped]
        public Dictionary<string, object> Data
        {
            get => string.IsNullOrEmpty(DataJson) 
                ? new Dictionary<string, object>() 
                : JsonSerializer.Deserialize<Dictionary<string, object>>(DataJson);
            set => DataJson = JsonSerializer.Serialize(value);
        }
        
        public virtual ICollection<PortEntity> Ports { get; set; } = new List<PortEntity>();
        
        public NodeModel ToModel()
        {
            var node = new NodeModel(new Point(X, Y))
            {
                Id = Id,
                Title = Title,
                Data = Data
            };
            
            foreach (var port in Ports)
            {
                node.AddPort(port.ToModel());
            }
            
            return node;
        }
        
        public static NodeEntity FromModel(NodeModel model, string diagramId)
        {
            var entity = new NodeEntity
            {
                Id = model.Id,
                DiagramId = diagramId,
                Title = model.Title,
                X = model.Position.X,
                Y = model.Position.Y,
                Data = model.Data as Dictionary<string, object>
            };
            
            foreach (var port in model.Ports)
            {
                entity.Ports.Add(PortEntity.FromModel(port, entity.Id));
            }
            
            return entity;
        }
    }
    
    public class PortEntity
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string NodeId { get; set; }
        
        [ForeignKey("NodeId")]
        public virtual NodeEntity Node { get; set; }
        
        public string Alignment { get; set; }
        
        public double X { get; set; }
        
        public double Y { get; set; }
        
        public PortModel ToModel()
        {
            var alignment = Enum.Parse<PortAlignment>(Alignment);
            var port = new PortModel(null, alignment, new Point(X, Y))
            {
                Id = Id
            };
            
            return port;
        }
        
        public static PortEntity FromModel(PortModel model, string nodeId)
        {
            return new PortEntity
            {
                Id = model.Id,
                NodeId = nodeId,
                Alignment = model.Alignment.ToString(),
                X = model.Position?.X ?? 0,
                Y = model.Position?.Y ?? 0
            };
        }
    }
    
    public class LinkEntity
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string DiagramId { get; set; }
        
        [ForeignKey("DiagramId")]
        public virtual DiagramEntity Diagram { get; set; }
        
        public string SourceNodeId { get; set; }
        
        public string SourcePortId { get; set; }
        
        public string TargetNodeId { get; set; }
        
        public string TargetPortId { get; set; }
        
        public string Color { get; set; }
        
        public double Width { get; set; } = 2;
        
        public string TargetMarker { get; set; }
        
        public string VerticesJson { get; set; }
        
        [NotMapped]
        public List<Point> Vertices
        {
            get => string.IsNullOrEmpty(VerticesJson) 
                ? new List<Point>() 
                : JsonSerializer.Deserialize<List<Point>>(VerticesJson);
            set => VerticesJson = JsonSerializer.Serialize(value);
        }
        
        public LinkModel ToModel(Dictionary<string, NodeModel> nodesMap)
        {
            if (!nodesMap.TryGetValue(SourceNodeId, out var sourceNode) ||
                !nodesMap.TryGetValue(TargetNodeId, out var targetNode))
            {
                return null;
            }
            
            var sourcePort = sourceNode.Ports.FirstOrDefault(p => p.Id == SourcePortId);
            var targetPort = targetNode.Ports.FirstOrDefault(p => p.Id == TargetPortId);
            
            if (sourcePort == null || targetPort == null)
            {
                return null;
            }
            
            var link = new LinkModel(sourcePort, targetPort)
            {
                Id = Id,
                Color = Color,
                Width = Width
            };
            
            if (!string.IsNullOrEmpty(TargetMarker))
            {
                link.TargetMarker = Enum.Parse<LinkMarker>(TargetMarker);
            }
            
            if (Vertices.Count > 0)
            {
                foreach (var vertex in Vertices)
                {
                    link.AddVertex(vertex);
                }
            }
            
            return link;
        }
        
        public static LinkEntity FromModel(LinkModel model, string diagramId)
        {
            var sourcePort = model.SourcePort;
            var targetPort = model.TargetPort;
            
            if (sourcePort == null || targetPort == null)
            {
                return null;
            }
            
            var sourceNode = sourcePort.Parent as NodeModel;
            var targetNode = targetPort.Parent as NodeModel;
            
            if (sourceNode == null || targetNode == null)
            {
                return null;
            }
            
            var entity = new LinkEntity
            {
                Id = model.Id,
                DiagramId = diagramId,
                SourceNodeId = sourceNode.Id,
                SourcePortId = sourcePort.Id,
                TargetNodeId = targetNode.Id,
                TargetPortId = targetPort.Id,
                Color = model.Color,
                Width = model.Width,
                TargetMarker = model.TargetMarker?.ToString(),
                Vertices = model.Vertices.Select(v => v.Position).ToList()
            };
            
            return entity;
        }
    }
}