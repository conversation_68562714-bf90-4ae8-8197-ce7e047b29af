using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a diagram node
/// </summary>
public class NodeEntity : BaseEntity
{
    [Required]
    public string DiagramId { get; set; } = string.Empty;
    
    [MaxLength(200)]
    public string? Title { get; set; }
    
    /// <summary>
    /// Node type for polymorphic deserialization
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string NodeType { get; set; } = string.Empty;
    
    /// <summary>
    /// Position X coordinate
    /// </summary>
    public double PositionX { get; set; }
    
    /// <summary>
    /// Position Y coordinate
    /// </summary>
    public double PositionY { get; set; }
    
    /// <summary>
    /// Width of the node
    /// </summary>
    public double? Width { get; set; }
    
    /// <summary>
    /// Height of the node
    /// </summary>
    public double? Height { get; set; }
    
    /// <summary>
    /// Whether the size is controlled
    /// </summary>
    public bool ControlledSize { get; set; }
    
    /// <summary>
    /// Whether the node is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the node is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Whether the node is selected
    /// </summary>
    public bool Selected { get; set; }
    
    /// <summary>
    /// Group ID if this node belongs to a group
    /// </summary>
    public string? GroupId { get; set; }
    
    /// <summary>
    /// Custom properties as JSON
    /// </summary>
    public string? PropertiesJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(DiagramId))]
    public virtual DiagramEntity? Diagram { get; set; }
    
    [ForeignKey(nameof(GroupId))]
    public virtual GroupEntity? Group { get; set; }
    
    public virtual ICollection<PortEntity> Ports { get; set; } = new List<PortEntity>();
}
