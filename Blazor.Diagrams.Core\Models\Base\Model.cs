﻿using System;
using Blazor.Diagrams.Core.Persistence;

namespace Blazor.Diagrams.Core.Models.Base;

public abstract class Model : IPersistable
{
    private bool _visible = true;

    protected Model() : this(Helpers.NanoidGenerator()) { }

    protected Model(string id)
    {
        Id = id;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public event Action<Model>? Changed;
    public event Action<Model>? VisibilityChanged;

    public string Id { get; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int Version { get; set; }
    public bool Locked { get; set; }
    public bool Visible
    {
        get => _visible;
        set
        {
            if (_visible == value)
                return;

            _visible = value;
            Touch();
            VisibilityChanged?.Invoke(this);
        }
    }

    public virtual void Refresh()
    {
        Touch();
        Changed?.Invoke(this);
    }

    /// <summary>
    /// Update the UpdatedAt timestamp
    /// </summary>
    protected virtual void Touch()
    {
        UpdatedAt = DateTime.UtcNow;
    }
}
