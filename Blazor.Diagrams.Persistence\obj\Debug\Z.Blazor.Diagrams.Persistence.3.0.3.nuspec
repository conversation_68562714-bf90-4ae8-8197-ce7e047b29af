﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Z.Blazor.Diagrams.Persistence</id>
    <version>3.0.3</version>
    <authors>zHaytam</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>ZBD.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://blazor-diagrams.zhaytam.com/</projectUrl>
    <description>Entity Framework persistence layer for Blazor.Diagrams</description>
    <tags>blazor diagrams persistence entityframework database</tags>
    <repository type="git" url="https://github.com/Blazor-Diagrams/Blazor.Diagrams" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Design" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Sqlite" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Design" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Sqlite" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Design" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Sqlite" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net9.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Design" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Sqlite" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net6.0\Blazor.Diagrams.Persistence.runtimeconfig.json" target="lib\net6.0\Blazor.Diagrams.Persistence.runtimeconfig.json" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net6.0\Blazor.Diagrams.Persistence.dll" target="lib\net6.0\Blazor.Diagrams.Persistence.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net7.0\Blazor.Diagrams.Persistence.runtimeconfig.json" target="lib\net7.0\Blazor.Diagrams.Persistence.runtimeconfig.json" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net7.0\Blazor.Diagrams.Persistence.dll" target="lib\net7.0\Blazor.Diagrams.Persistence.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net8.0\Blazor.Diagrams.Persistence.runtimeconfig.json" target="lib\net8.0\Blazor.Diagrams.Persistence.runtimeconfig.json" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net8.0\Blazor.Diagrams.Persistence.dll" target="lib\net8.0\Blazor.Diagrams.Persistence.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net9.0\Blazor.Diagrams.Persistence.runtimeconfig.json" target="lib\net9.0\Blazor.Diagrams.Persistence.runtimeconfig.json" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Persistence\bin\Debug\net9.0\Blazor.Diagrams.Persistence.dll" target="lib\net9.0\Blazor.Diagrams.Persistence.dll" />
    <file src="D:\projects\MudBlazor\ZBD.png" target="\ZBD.png" />
    <file src="D:\projects\MudBlazor\README.md" target="\README.md" />
  </files>
</package>