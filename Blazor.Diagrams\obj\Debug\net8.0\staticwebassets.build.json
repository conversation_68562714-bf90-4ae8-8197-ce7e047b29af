{"Version": 1, "Hash": "Ad+MIRXR0Tug7uX+8ae+LBGeFmIx4BmFvz/ZWsxMTzo=", "Source": "Z.Blazor.Diagrams", "BasePath": "_content/Z.Blazor.Diagrams", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Z.Blazor.Diagrams\\wwwroot", "Source": "Z.Blazor.Diagrams", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "Pattern": "**"}], "Assets": [{"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.<PERSON>lazor.Diagrams#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "wxhkjam3jz", "Integrity": "1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "FileLength": 81, "LastWriteTime": "2025-06-27T14:38:11+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.<PERSON>.Diagrams#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "wxhkjam3jz", "Integrity": "1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 81, "LastWriteTime": "2025-06-27T14:38:11+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kr4r5y5l5h", "Integrity": "IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.css", "FileLength": 3445, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pwzqlbbfs", "Integrity": "tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.min.css", "FileLength": 2533, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c5cp0u3gkb", "Integrity": "QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.js", "FileLength": 2034, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u872bpsf3j", "Integrity": "LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.min.js", "FileLength": 1071, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9j2o0uhpet", "Integrity": "K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.css", "FileLength": 1939, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjpcwcpl0m", "Integrity": "UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.min.css", "FileLength": 1327, "LastWriteTime": "2025-06-27T14:31:03+00:00"}], "Endpoints": [{"Route": "default.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.kr4r5y5l5h.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "label", "Value": "default.styles.css"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.min.6pwzqlbbfs.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "label", "Value": "default.styles.min.css"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "script.c5cp0u3gkb.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "label", "Value": "script.js"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.min.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "script.min.js"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "style.9j2o0uhpet.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "label", "Value": "style.css"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.min.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "style.min.css"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}]}