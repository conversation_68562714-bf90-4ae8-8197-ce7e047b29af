using DiagramEditor.Models;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models;

namespace DiagramEditor.Tests;

public class CustomNodeModelsTests
{
    [Fact]
    public void RectangleNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(100, 50);

        // Act
        var node = new RectangleNodeModel(position);

        // Assert
        Assert.Equal("Retângulo", node.Title);
        Assert.Equal(position, node.Position);
        Assert.Equal(120, node.Size?.Width);
        Assert.Equal(60, node.Size?.Height);
        Assert.Equal(4, node.Ports.Count); // Top, Bottom, Left, Right
        Assert.Equal("#2196F3", node.BackgroundColor);
        Assert.Equal("#1976D2", node.BorderColor);
        Assert.Equal(2, node.BorderWidth);
        Assert.Equal("#FFFFFF", node.TextColor);
    }

    [Fact]
    public void CircleNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(200, 100);

        // Act
        var node = new CircleNodeModel(position);

        // Assert
        Assert.Equal("Círculo", node.Title);
        Assert.Equal(position, node.Position);
        Assert.Equal(80, node.Size?.Width);
        Assert.Equal(80, node.Size?.Height);
        Assert.Equal(4, node.Ports.Count);
        Assert.Equal("#4CAF50", node.BackgroundColor);
    }

    [Fact]
    public void TriangleNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(300, 150);

        // Act
        var node = new TriangleNodeModel(position);

        // Assert
        Assert.Equal("Triângulo", node.Title);
        Assert.Equal(position, node.Position);
        Assert.Equal(100, node.Size?.Width);
        Assert.Equal(80, node.Size?.Height);
        Assert.Equal("#FF9800", node.BackgroundColor);
    }

    [Fact]
    public void DiamondNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(400, 200);

        // Act
        var node = new DiamondNodeModel(position);

        // Assert
        Assert.Equal("Losango", node.Title);
        Assert.Equal(position, node.Position);
        Assert.Equal("#9C27B0", node.BackgroundColor);
    }

    [Fact]
    public void ProcessNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(500, 250);

        // Act
        var node = new ProcessNodeModel(position);

        // Assert
        Assert.Equal("Processo", node.Title);
        Assert.Equal("Manual", node.ProcessType);
        Assert.Equal("", node.Description);
        Assert.Equal("#607D8B", node.BackgroundColor);
    }

    [Fact]
    public void DecisionNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(600, 300);

        // Act
        var node = new DecisionNodeModel(position);

        // Assert
        Assert.Equal("Decisão", node.Title);
        Assert.Equal("", node.Question);
        Assert.Equal("Sim", node.YesLabel);
        Assert.Equal("Não", node.NoLabel);
        Assert.Equal("#F44336", node.BackgroundColor);
    }

    [Fact]
    public void StartEndNodeModel_StartNode_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(700, 350);

        // Act
        var node = new StartEndNodeModel(position, isStart: true);

        // Assert
        Assert.Equal("Início", node.Title);
        Assert.True(node.IsStart);
        Assert.Single(node.Ports); // Only bottom port for start
        Assert.Equal(PortAlignment.Bottom, node.Ports.First().Alignment);
    }

    [Fact]
    public void StartEndNodeModel_EndNode_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(800, 400);

        // Act
        var node = new StartEndNodeModel(position, isStart: false);

        // Assert
        Assert.Equal("Fim", node.Title);
        Assert.False(node.IsStart);
        Assert.Single(node.Ports); // Only top port for end
        Assert.Equal(PortAlignment.Top, node.Ports.First().Alignment);
    }

    [Fact]
    public void CommentNodeModel_Constructor_SetsCorrectDefaults()
    {
        // Arrange
        var position = new Point(900, 450);

        // Act
        var node = new CommentNodeModel(position);

        // Assert
        Assert.Equal("Comentário", node.Title);
        Assert.Equal("", node.Comment);
        Assert.True(node.IsSticky);
        Assert.Single(node.Ports); // Only left port for comments
        Assert.Equal(PortAlignment.Left, node.Ports.First().Alignment);
        Assert.Equal("#FFF9C4", node.BackgroundColor);
        Assert.Equal("#333333", node.TextColor);
    }

    [Theory]
    [InlineData(0, 0)]
    [InlineData(100, 200)]
    [InlineData(-50, -100)]
    public void AllNodeTypes_AcceptDifferentPositions(double x, double y)
    {
        // Arrange
        var position = new Point(x, y);

        // Act & Assert
        var rectangleNode = new RectangleNodeModel(position);
        var circleNode = new CircleNodeModel(position);
        var triangleNode = new TriangleNodeModel(position);
        var diamondNode = new DiamondNodeModel(position);

        Assert.Equal(position, rectangleNode.Position);
        Assert.Equal(position, circleNode.Position);
        Assert.Equal(position, triangleNode.Position);
        Assert.Equal(position, diamondNode.Position);
    }

    [Fact]
    public void CustomNodeProperties_CanBeModified()
    {
        // Arrange
        var node = new RectangleNodeModel(new Point(0, 0));

        // Act
        node.BackgroundColor = "#FF0000";
        node.BorderColor = "#00FF00";
        node.BorderWidth = 5;
        node.TextColor = "#0000FF";

        // Assert
        Assert.Equal("#FF0000", node.BackgroundColor);
        Assert.Equal("#00FF00", node.BorderColor);
        Assert.Equal(5, node.BorderWidth);
        Assert.Equal("#0000FF", node.TextColor);
    }
}
