{"version": 3, "targets": {"net6.0": {"Nanoid/3.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.0"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net7.0": {"Nanoid/3.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.0"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net8.0": {"Nanoid/3.1.0": {"type": "package", "compile": {"lib/net8.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.0"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net9.0": {"Nanoid/3.1.0": {"type": "package", "compile": {"lib/net8.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.1.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.0"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}}, "libraries": {"Nanoid/3.1.0": {"sha512": "5vhxZ+1iVH213boPsR3KYQXWObZQcaGvLU9ytTroWJFeVzV2EFAtGgrmMpb8BCxd+KWeBASniZLzd3UQ0tkhRQ==", "type": "package", "path": "nanoid/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net48/Nanoid.dll", "lib/net48/Nanoid.xml", "lib/net8.0/Nanoid.dll", "lib/net8.0/Nanoid.xml", "lib/netstandard2.0/Nanoid.dll", "lib/netstandard2.0/Nanoid.xml", "nanoid.3.1.0.nupkg.sha512", "nanoid.nuspec"]}, "SvgPathProperties/1.1.0": {"sha512": "9EE1r+9CkULhO5VYtCKnppSzMyXSr4ziATif2u+AUvHz9vAX4WHmHH52dWzo92QyELl2GWvopdtQLkCheO2OsA==", "type": "package", "path": "svgpathproperties/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/SvgPathProperties.dll", "svgpathproperties.1.1.0.nupkg.sha512", "svgpathproperties.nuspec"]}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "path": "../Blazor.Diagrams.Core/Blazor.Diagrams.Core.csproj", "msbuildProject": "../Blazor.Diagrams.Core/Blazor.Diagrams.Core.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["Z.Blazor.Diagrams.Core >= 3.0.3"], "net7.0": ["Z.Blazor.Diagrams.Core >= 3.0.3"], "net8.0": ["Z.Blazor.Diagrams.Core >= 3.0.3"], "net9.0": ["Z.Blazor.Diagrams.Core >= 3.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "projectName": "Z.Blazor.Diagrams.Algorithms", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"coverlet.collector": "6.0.0", "Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Microsoft.EntityFrameworkCore": "6.0.0", "Microsoft.EntityFrameworkCore.Design": "6.0.0", "Microsoft.EntityFrameworkCore.InMemory": "6.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.0", "Microsoft.EntityFrameworkCore.Tools": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "MudBlazor": "6.11.2", "Nanoid": "3.1.0", "SvgPathProperties": "1.1.0", "xunit": "2.6.1", "xunit.runner.visualstudio": "2.5.3"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}