{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "DiagramEditor.styles.css", "AssetFile": "DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:31:36 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css", "AssetFile": "DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:31:36 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}, {"Name": "label", "Value": "DiagramEditor.styles.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "508754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44027"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:38:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2978"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}]}, {"Route": "css/site.zh9s866nx2.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2978"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh9s866nx2"}, {"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js", "AssetFile": "js/diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}, {"Name": "label", "Value": "js/diagram-export.js"}]}, {"Route": "js/diagram-export.js", "AssetFile": "js/diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}]}