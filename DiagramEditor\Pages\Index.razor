﻿@page "/"

<PageTitle>Editor de Diagramas</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" Class="mb-4">Bem-vindo ao Editor de Diagramas</MudText>

    <MudGrid>
        <MudItem xs="12" md="6">
            <MudCard>
                <MudCardContent>
                    <MudText Typo="Typo.h5" Class="mb-2">Criar Novo Diagrama</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">
                        Comece criando um novo diagrama com nós e conexões ortogonais.
                    </MudText>
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Primary"
                             StartIcon="@Icons.Material.Filled.Add"
                             Href="/diagram-editor">
                        Novo Diagrama
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6">
            <MudCard>
                <MudCardContent>
                    <MudText Typo="Typo.h5" Class="mb-2">Diagramas Salvos</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">
                        Visualize e edite seus diagramas salvos anteriormente.
                    </MudText>
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Secondary"
                             StartIcon="@Icons.Material.Filled.FolderOpen"
                             Href="/saved-diagrams">
                        Ver Salvos
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <MudPaper Class="pa-4 mt-6" Elevation="1">
        <MudText Typo="Typo.h6" Class="mb-2">Recursos</MudText>
        <MudList>
            <MudListItem Icon="@Icons.Material.Filled.Circle" Text="Adicionar e remover nós" />
            <MudListItem Icon="@Icons.Material.Filled.Circle" Text="Conectar nós com links ortogonais" />
            <MudListItem Icon="@Icons.Material.Filled.Circle" Text="Salvar e carregar diagramas" />
            <MudListItem Icon="@Icons.Material.Filled.Circle" Text="Interface moderna com MudBlazor" />
            <MudListItem Icon="@Icons.Material.Filled.Circle" Text="Persistência em banco de dados SQLite" />
        </MudList>
    </MudPaper>
</MudContainer>
