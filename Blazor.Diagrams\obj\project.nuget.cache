{"version": 2, "dgSpecHash": "V1S+zhmOdAo=", "success": true, "projectFilePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\3.0.0\\microsoft.aspnetcore.authorization.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\3.0.0\\microsoft.aspnetcore.components.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\3.0.0\\microsoft.aspnetcore.components.analyzers.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\3.0.0\\microsoft.aspnetcore.components.forms.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\3.0.0\\microsoft.aspnetcore.components.web.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\3.0.0\\microsoft.aspnetcore.metadata.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\3.0.0\\microsoft.extensions.dependencyinjection.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\3.0.0\\microsoft.extensions.dependencyinjection.abstractions.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\3.0.0\\microsoft.extensions.logging.abstractions.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\3.0.0\\microsoft.extensions.options.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\3.0.0\\microsoft.extensions.primitives.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\3.0.0\\microsoft.jsinterop.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nanoid\\1.0.0\\nanoid.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\svgpathproperties\\1.0.0\\svgpathproperties.1.0.0.nupkg.sha512"], "logs": [{"code": "NU1604", "level": "Warning", "message": "Project dependency Microsoft.AspNetCore.Components does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "libraryId": "Microsoft.AspNetCore.Components", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1604", "level": "Warning", "message": "Project dependency Microsoft.AspNetCore.Components.Web does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "libraryId": "Microsoft.AspNetCore.Components.Web", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1602", "level": "Warning", "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency Nanoid. Nanoid 1.0.0 was resolved instead.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "libraryId": "Nanoid", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency SvgPathProperties. SvgPathProperties 1.0.0 was resolved instead.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "libraryId": "SvgPathProperties", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}]}