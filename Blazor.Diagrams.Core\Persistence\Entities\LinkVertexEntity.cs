using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a link vertex (bend point)
/// </summary>
public class LinkVertexEntity : BaseEntity
{
    [Required]
    public string LinkId { get; set; } = string.Empty;
    
    /// <summary>
    /// Position X coordinate
    /// </summary>
    public double PositionX { get; set; }
    
    /// <summary>
    /// Position Y coordinate
    /// </summary>
    public double PositionY { get; set; }
    
    /// <summary>
    /// Order of this vertex in the link
    /// </summary>
    public int Order { get; set; }
    
    /// <summary>
    /// Whether the vertex is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the vertex is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Whether the vertex is selected
    /// </summary>
    public bool Selected { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(LinkId))]
    public virtual LinkEntity? Link { get; set; }
}
