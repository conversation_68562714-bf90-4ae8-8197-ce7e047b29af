﻿.default-node {
    width: 100px;
    height: 80px;
    border-radius: 10px;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    -webkit-box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, .12);
    box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, .12);
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.default-node.selected {
    border: 1px solid #6e9fd4;
}

    .default-node.selected .diagram-port {
        border: 1px solid #6e9fd4;
    }

    .default-node .diagram-port, .default.diagram-group .diagram-port {
        width: 20px;
        height: 20px;
        margin: -10px;
        border-radius: 50%;
        background-color: #f5f5f5;
        border: 1px solid #d4d4d4;
        cursor: pointer;
        position: absolute;
    }

    .default-node .diagram-port:hover, .default-node .diagram-port.has-links, .default.diagram-group .diagram-port.has-links {
        background-color: black;
    }

    .default-node .diagram-port.bottom, .default.diagram-group .diagram-port.bottom {
        bottom: 0px;
        left: 50%;
    }

    .default-node .diagram-port.bottomleft, .default.diagram-group .diagram-port.bottomleft {
        bottom: 0px;
        left: 0px;
    }

.default-node .diagram-port.bottomright, .default.diagram-group .diagram-port.bottomright {
    bottom: 0px;
    right: 0px;
}

.default-node .diagram-port.top, .default.diagram-group .diagram-port.top {
    top: 0px;
    left: 50%;
}

.default-node .diagram-port.topleft, .default.diagram-group .diagram-port.topleft {
    top: 0px;
    left: 0px;
}

.default-node .diagram-port.topright, .default.diagram-group .diagram-port.topright {
    top: 0px;
    right: 0px;
}

.default-node .diagram-port.left, .default.diagram-group .diagram-port.left {
    left: 0px;
    top: 50%;
}

.default-node .diagram-port.right, .default.diagram-group .diagram-port.right {
    right: 0px;
    top: 50%;
}

.diagram-navigator.default {
    position: absolute;
    bottom: 10px;
    right: 10px;
    border: 3px solid #9BA8B0;
    border-radius: 15px;
    padding: 20px;
    background-color: white;
}

div.diagram-group.default {
    outline: 2px solid black;
    background: rgb(198, 198, 198);
}

div.diagram-group.default.selected {
    outline: 2px solid #6e9fd4;
}

g.diagram-group.default rect {
    outline: 2px solid black;
    fill: rgb(198, 198, 50);
}

g.diagram-group.default.selected > rect {
    outline: 2px solid green;
}

.diagram-link div.default-link-label {
    display: inline-block;
    color: #fff;
    background-color: rgb(110, 159, 212);
    border-radius: 0.25rem;
    padding: 0.25rem;
    text-align: center;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    min-width: 3rem;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

/*# sourceMappingURL=wwwroot\default.styles.css.map */
