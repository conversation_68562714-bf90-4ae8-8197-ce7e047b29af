{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Z.Blazor.Diagrams.styles.css", "AssetFile": "Z.Blazor.Diagrams.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.styles.css", "AssetFile": "Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.styles.css.gz", "AssetFile": "Z.Blazor.Diagrams.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css", "AssetFile": "Z.Blazor.Diagrams.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css"}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css", "AssetFile": "Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css"}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css.gz", "AssetFile": "Z.Blazor.Diagrams.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css.gz"}]}, {"Route": "default.styles.css", "AssetFile": "default.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.css", "AssetFile": "default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.css.gz", "AssetFile": "default.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}]}, {"Route": "default.styles.kr4r5y5l5h.css", "AssetFile": "default.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "default.styles.css"}]}, {"Route": "default.styles.kr4r5y5l5h.css", "AssetFile": "default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "default.styles.css"}]}, {"Route": "default.styles.kr4r5y5l5h.css.gz", "AssetFile": "default.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}, {"Name": "label", "Value": "default.styles.css.gz"}]}, {"Route": "default.styles.min.6pwzqlbbfs.css", "AssetFile": "default.styles.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "default.styles.min.css"}]}, {"Route": "default.styles.min.6pwzqlbbfs.css", "AssetFile": "default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "default.styles.min.css"}]}, {"Route": "default.styles.min.6pwzqlbbfs.css.gz", "AssetFile": "default.styles.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}, {"Name": "label", "Value": "default.styles.min.css.gz"}]}, {"Route": "default.styles.min.css", "AssetFile": "default.styles.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.css", "AssetFile": "default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.css.gz", "AssetFile": "default.styles.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}]}, {"Route": "script.c5cp0u3gkb.js", "AssetFile": "script.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "script.js"}]}, {"Route": "script.c5cp0u3gkb.js", "AssetFile": "script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "script.js"}]}, {"Route": "script.c5cp0u3gkb.js.gz", "AssetFile": "script.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}, {"Name": "label", "Value": "script.js.gz"}]}, {"Route": "script.js", "AssetFile": "script.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.js", "AssetFile": "script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.js.gz", "AssetFile": "script.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}]}, {"Route": "script.min.js", "AssetFile": "script.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.js", "AssetFile": "script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.js.gz", "AssetFile": "script.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "script.min.u872bpsf3j.js", "AssetFile": "script.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "script.min.js"}]}, {"Route": "script.min.u872bpsf3j.js", "AssetFile": "script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "script.min.js"}]}, {"Route": "script.min.u872bpsf3j.js.gz", "AssetFile": "script.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}, {"Name": "label", "Value": "script.min.js.gz"}]}, {"Route": "style.9j2o0uhpet.css", "AssetFile": "style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "style.css"}]}, {"Route": "style.9j2o0uhpet.css", "AssetFile": "style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "style.css"}]}, {"Route": "style.9j2o0uhpet.css.gz", "AssetFile": "style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}, {"Name": "label", "Value": "style.css.gz"}]}, {"Route": "style.css", "AssetFile": "style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.css", "AssetFile": "style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.css.gz", "AssetFile": "style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}]}, {"Route": "style.min.css", "AssetFile": "style.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.css", "AssetFile": "style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.css.gz", "AssetFile": "style.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "style.min.kjpcwcpl0m.css", "AssetFile": "style.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "style.min.css"}]}, {"Route": "style.min.kjpcwcpl0m.css", "AssetFile": "style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "style.min.css"}]}, {"Route": "style.min.kjpcwcpl0m.css.gz", "AssetFile": "style.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}, {"Name": "label", "Value": "style.min.css.gz"}]}]}