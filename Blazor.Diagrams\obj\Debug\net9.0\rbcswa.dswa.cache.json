{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["LSKuZLehKnZe+SfdvBjU+ss7+NPBR0I/4zD30lOG+XI=", "9VQkDBLKAPl0/iybm7lGOq/Me3DEbVwv9E8n9lQ4g/Y=", "DZTyyiJdmyiAgz9hkkT5DAwg3kvIL/QKPX84ZdcfUL0=", "qf5WWKZ4wXv3wTYqke3ZtphGC3ViHfJB/1aZOfuYPzU=", "aL3hMnKuVfPEVaofhuQNSsxDq9Dgr3AO//3f3lYKCg4=", "eIK+xwts/ABVyKtlY1RbUv+lFGpk7/9jOrCx0Tc+ksY=", "1jYboYYQrSb9rGpEZe8O91DH5kuXwD0Jen9xy90JW4A=", "IMy7tyXhgMTQQfr5TTydyoTzZEXwOm2ekqtwlq6JMbA="], "CachedAssets": {"IMy7tyXhgMTQQfr5TTydyoTzZEXwOm2ekqtwlq6JMbA=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 93, "LastWriteTime": "2025-07-10T01:46:41.4024917+00:00"}, "1jYboYYQrSb9rGpEZe8O91DH5kuXwD0Jen9xy90JW4A=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "FileLength": 93, "LastWriteTime": "2025-07-10T01:46:41.4034916+00:00"}, "eIK+xwts/ABVyKtlY1RbUv+lFGpk7/9jOrCx0Tc+ksY=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "FileLength": 418, "LastWriteTime": "2025-07-10T01:46:41.4024917+00:00"}, "aL3hMnKuVfPEVaofhuQNSsxDq9Dgr3AO//3f3lYKCg4=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint=9j2o0uhpet}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yjpzo0a57p", "Integrity": "HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "FileLength": 510, "LastWriteTime": "2025-07-10T01:46:41.4034916+00:00"}, "qf5WWKZ4wXv3wTYqke3ZtphGC3ViHfJB/1aZOfuYPzU=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "FileLength": 520, "LastWriteTime": "2025-07-10T01:46:41.4024917+00:00"}, "DZTyyiJdmyiAgz9hkkT5DAwg3kvIL/QKPX84ZdcfUL0=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint=c5cp0u3gkb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9r2jwh9rj", "Integrity": "g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "FileLength": 651, "LastWriteTime": "2025-07-10T01:46:41.4034916+00:00"}, "9VQkDBLKAPl0/iybm7lGOq/Me3DEbVwv9E8n9lQ4g/Y=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint=6pwzqlbbfs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhqk7gbln5", "Integrity": "7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "FileLength": 680, "LastWriteTime": "2025-07-10T01:46:41.4024917+00:00"}, "LSKuZLehKnZe+SfdvBjU+ss7+NPBR0I/4zD30lOG+XI=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint=kr4r5y5l5h}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "469wz5lhr2", "Integrity": "bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "FileLength": 838, "LastWriteTime": "2025-07-10T01:46:41.4014914+00:00"}}, "CachedCopyCandidates": {}}