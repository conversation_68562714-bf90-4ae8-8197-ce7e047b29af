{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["E+hKqFE+pdgl9wEFmGfBzlocpzkyfeb2FbgSFJ8oCYQ=", "cLK1HqdW+ctHBshz2W6up9qdIQf0FMNGT9MZQsqg0+M=", "SH12oBeKbG5qZv9sJlmACMfqVHvL20LsztuvhL/GhE8=", "lw39UUBHInZQaRR0J4o7Mx804z71W5zs7qVXSjOyR+I=", "mmtGqZZf5dmYxPnVu0PNwv1KJHvE5dXEgYAQ7jglMl0=", "G7L4msdgBMJnK7b9JBFSDBxlFEwFctyJjxxjKbVm9ag=", "Amt0Pe9RGVCt073AwW7vYPnJrss4E6fY53qodHnkvSk=", "B7RBT//BsCPo/0WY9k4blHuFZSnGLyrB6ncTX7V0ZdU="], "CachedAssets": {"E+hKqFE+pdgl9wEFmGfBzlocpzkyfeb2FbgSFJ8oCYQ=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint=kr4r5y5l5h}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "469wz5lhr2", "Integrity": "bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.css", "FileLength": 838, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "cLK1HqdW+ctHBshz2W6up9qdIQf0FMNGT9MZQsqg0+M=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint=6pwzqlbbfs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhqk7gbln5", "Integrity": "7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "FileLength": 680, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "SH12oBeKbG5qZv9sJlmACMfqVHvL20LsztuvhL/GhE8=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint=c5cp0u3gkb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9r2jwh9rj", "Integrity": "g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.js", "FileLength": 651, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "lw39UUBHInZQaRR0J4o7Mx804z71W5zs7qVXSjOyR+I=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\script.min.js", "FileLength": 520, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "mmtGqZZf5dmYxPnVu0PNwv1KJHvE5dXEgYAQ7jglMl0=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint=9j2o0uhpet}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yjpzo0a57p", "Integrity": "HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.css", "FileLength": 510, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "G7L4msdgBMJnK7b9JBFSDBxlFEwFctyJjxxjKbVm9ag=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\style.min.css", "FileLength": 418, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "Amt0Pe9RGVCt073AwW7vYPnJrss4E6fY53qodHnkvSk=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "FileLength": 93, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}, "B7RBT//BsCPo/0WY9k4blHuFZSnGLyrB6ncTX7V0ZdU=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 93, "LastWriteTime": "2025-06-27T14:37:31.653091+00:00"}}, "CachedCopyCandidates": {}}