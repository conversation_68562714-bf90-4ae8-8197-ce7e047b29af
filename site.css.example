/* Estilos para o editor de diagramas */

/* Estilo para o canvas do diagrama */
.diagram-canvas {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    background-image: linear-gradient(#e0e0e0 1px, transparent 1px),
                      linear-gradient(90deg, #e0e0e0 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Estilos para os nós personalizados */
.custom-node {
    min-width: 150px;
    min-height: 80px;
    padding: 10px;
    border: 2px solid #000000;
    border-radius: 5px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
}

.custom-node.selected {
    box-shadow: 0 0 0 2px #3D8BFD, 0 2px 5px rgba(0, 0, 0, 0.2);
}

.node-title {
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

/* Estilos para as portas dos nós */
.custom-port {
    width: 10px;
    height: 10px;
    background-color: #3D8BFD;
    border-radius: 50%;
    border: 2px solid #ffffff;
    position: absolute;
    transition: transform 0.2s ease, background-color 0.2s ease;
}

.custom-port:hover {
    transform: scale(1.5);
    background-color: #0D6EFD;
    cursor: pointer;
}

/* Posicionamento das portas */
.port-top {
    top: -7px;
    left: 50%;
    transform: translateX(-50%);
}

.port-right {
    right: -7px;
    top: 50%;
    transform: translateY(-50%);
}

.port-bottom {
    bottom: -7px;
    left: 50%;
    transform: translateX(-50%);
}

.port-left {
    left: -7px;
    top: 50%;
    transform: translateY(-50%);
}

/* Estilos para os links */
.diagram-link {
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}

.diagram-link.selected {
    stroke-dasharray: 5;
    animation: dash 5s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: 1000;
    }
}

/* Estilos para os marcadores de links */
.link-marker-arrow {
    fill: currentColor;
}

.link-marker-circle {
    fill: currentColor;
    stroke: none;
}

.link-marker-square {
    fill: currentColor;
    stroke: none;
}

/* Estilos para o painel de propriedades */
.properties-panel {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    background-color: #ffffff;
}

/* Estilos responsivos */
@media (max-width: 600px) {
    .custom-node {
        min-width: 120px;
        min-height: 60px;
        padding: 5px;
    }
    
    .node-title {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
}