{"version": 3, "targets": {"net6.0": {"Microsoft.AspNetCore.Authorization/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "3.0.0", "Microsoft.AspNetCore.Components.Analyzers": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/3.0.0": {"type": "package", "build": {"build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.props": {}}}, "Microsoft.AspNetCore.Components.Forms/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0", "Microsoft.AspNetCore.Components.Forms": "3.0.0", "Microsoft.Extensions.DependencyInjection": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Primitives": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.JSInterop/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Nanoid": "(, )", "SvgPathProperties": "(, )"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net7.0": {"Microsoft.AspNetCore.Authorization/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "3.0.0", "Microsoft.AspNetCore.Components.Analyzers": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/3.0.0": {"type": "package", "build": {"build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.props": {}}}, "Microsoft.AspNetCore.Components.Forms/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0", "Microsoft.AspNetCore.Components.Forms": "3.0.0", "Microsoft.Extensions.DependencyInjection": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Primitives": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.JSInterop/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"Nanoid": "(, )", "SvgPathProperties": "(, )"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net8.0": {"Microsoft.AspNetCore.Authorization/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "3.0.0", "Microsoft.AspNetCore.Components.Analyzers": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/3.0.0": {"type": "package", "build": {"build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.props": {}}}, "Microsoft.AspNetCore.Components.Forms/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0", "Microsoft.AspNetCore.Components.Forms": "3.0.0", "Microsoft.Extensions.DependencyInjection": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Primitives": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.JSInterop/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Nanoid": "(, )", "SvgPathProperties": "(, )"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}, "net9.0": {"Microsoft.AspNetCore.Authorization/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "3.0.0", "Microsoft.AspNetCore.Components.Analyzers": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/3.0.0": {"type": "package", "build": {"build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.props": {}}}, "Microsoft.AspNetCore.Components.Forms/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/3.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "3.0.0", "Microsoft.AspNetCore.Components.Forms": "3.0.0", "Microsoft.Extensions.DependencyInjection": "3.0.0", "Microsoft.JSInterop": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/3.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/3.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Primitives": "3.0.0"}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.JSInterop/3.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Nanoid": "(, )", "SvgPathProperties": "(, )"}, "compile": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}, "runtime": {"bin/placeholder/Z.Blazor.Diagrams.Core.dll": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authorization/3.0.0": {"sha512": "pylAplpaC5r3YIh2OR4RcYCSYoMc/sZwHyGeyT43IbSMN+TqvO05pAybaFcNxPeZ+HxDP2LcRO/R1XHSCB2GKw==", "type": "package", "path": "microsoft.aspnetcore.authorization/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.dll", "lib/netcoreapp3.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.3.0.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/3.0.0": {"sha512": "aH23865LRGx21C36zNY0xcWzYRvQIqXsUzcR/9BIH/UezsNV2eWOKQaCkQItzo2RG2ebRkWk6livhc0VLeEt7Q==", "type": "package", "path": "microsoft.aspnetcore.components/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "THIRD-PARTY-NOTICES.txt", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.dll", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Components.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.3.0.0.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/3.0.0": {"sha512": "XqYERMlbxaVzcab5NVv1OyVh8KzEq8cSpfk2QSS6/9qtlkPEuNoDsEyPVlkIDktev66PhRhreq2W85r2siezmQ==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.props", "microsoft.aspnetcore.components.analyzers.3.0.0.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/3.0.0": {"sha512": "oEFexkEnQTIedyfBU2FA16cPrEesBj+sa73Xr9V7xnqu2Tl7alHdCA/u0oJhzYsmY4qpcN2GA15yeAl4fNXOCw==", "type": "package", "path": "microsoft.aspnetcore.components.forms/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "THIRD-PARTY-NOTICES.txt", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Forms.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.3.0.0.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/3.0.0": {"sha512": "Efu7Lj0T6MONxkgp/33rbcIErg7x9Uy97/jmOF2AaUJ5cGOuQFKLMvkOL3gR7qEYoodr8XE49Tm8cE/CHJHZhQ==", "type": "package", "path": "microsoft.aspnetcore.components.web/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "THIRD-PARTY-NOTICES.txt", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.dll", "lib/netcoreapp3.0/Microsoft.AspNetCore.Components.Web.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Components.Web.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.3.0.0.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Metadata/3.0.0": {"sha512": "gXT2t+bocyv4aCwwfrATy8QMHHZTRqZ/grmUzC+76MA1M+S0TIXp+BK3dEkvADVkhsAdERb5DnjqQKX3G/uMYQ==", "type": "package", "path": "microsoft.aspnetcore.metadata/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.3.0.0.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Extensions.DependencyInjection/3.0.0": {"sha512": "yDsuNA/BT4j9qrcRs0NUNHQAJfywFWX18ZZ+shxXJL+/nIfz3vhuRTfnYgvFeQlNBlgmgdSjOcs4ajgoS6Q/Ng==", "type": "package", "path": "microsoft.extensions.dependencyinjection/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netcoreapp3.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.3.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.0.0": {"sha512": "ofQRroDlzJ0xKOtzNuaVt6QKNImFkhkG0lIMpGl7PtXnIf5SuLWBeiQZAP8DNSxDBJJdcsPkiJiMYK2WA5H8dQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.3.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec"]}, "Microsoft.Extensions.Logging.Abstractions/3.0.0": {"sha512": "+PsosTYZn+omucI0ff9eywo9QcPLwcbIWf7dz7ZLM1zGR8gVZXJ3wo6+tkuIedUNW5iWENlVJPEvrGjiVeoNNQ==", "type": "package", "path": "microsoft.extensions.logging.abstractions/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.3.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Extensions.Options/3.0.0": {"sha512": "aZuVhN/TC872Yb55nrb7an82sfSAdNYxIyzu3zbYHOnhwal5hdkBUxzuoYj1khI2sw0tWq6i82i624zEFmiJhg==", "type": "package", "path": "microsoft.extensions.options/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Microsoft.Extensions.Options.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.3.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec"]}, "Microsoft.Extensions.Primitives/3.0.0": {"sha512": "6gwewTbmOh+ZVBicVkL1XRp79sx4O7BVY6Yy+7OYZdwn3pyOKe9lOam+3gXJ3TZMjhJZdV0Ub8hxHt2vkrmN5Q==", "type": "package", "path": "microsoft.extensions.primitives/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.3.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec"]}, "Microsoft.JSInterop/3.0.0": {"sha512": "1MsiDhjO/GUXYzvhjDGs+xvVNHxsGZ1mlpIAhdVEvmVP5GHiUGoy3DcJVRewmfasyfD3nfFnuLiteLZXlF1ajw==", "type": "package", "path": "microsoft.jsinterop/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.0/Microsoft.JSInterop.dll", "lib/netcoreapp3.0/Microsoft.JSInterop.xml", "lib/netstandard2.0/Microsoft.JSInterop.dll", "lib/netstandard2.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.3.0.0.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Nanoid/1.0.0": {"sha512": "HPFfNLCehToYlmYz+8wJKwl5EK9E/HJ8Jv2JUa3zLlKkuo9xLJF1w0Zdb1c5bee8yRj4W71Xsc10TiB0HUxdrA==", "type": "package", "path": "nanoid/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Nanoid.dll", "lib/netstandard2.0/Nanoid.xml", "nanoid.1.0.0.nupkg.sha512", "nanoid.nuspec"]}, "SvgPathProperties/1.0.0": {"sha512": "vRoBafA/voS5oy2MxM4CU4cz0SDAl+5pk1MA4botTW9yyI80SF0gyO/tBfPEQ+NT0OrMdSAOMJv0brfBfR8wyw==", "type": "package", "path": "svgpathproperties/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SvgPathProperties.dll", "svgpathproperties.1.0.0.nupkg.sha512", "svgpathproperties.nuspec"]}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "path": "../Blazor.Diagrams.Core/Blazor.Diagrams.Core.csproj", "msbuildProject": "../Blazor.Diagrams.Core/Blazor.Diagrams.Core.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["Microsoft.AspNetCore.Components", "Microsoft.AspNetCore.Components.Web", "Z.Blazor.Diagrams.Core >= 3.0.3"], "net7.0": ["Microsoft.AspNetCore.Components", "Microsoft.AspNetCore.Components.Web", "Z.Blazor.Diagrams.Core >= 3.0.3"], "net8.0": ["Microsoft.AspNetCore.Components", "Microsoft.AspNetCore.Components.Web", "Z.Blazor.Diagrams.Core >= 3.0.3"], "net9.0": ["Microsoft.AspNetCore.Components", "Microsoft.AspNetCore.Components.Web", "Z.Blazor.Diagrams.Core >= 3.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "projectName": "Z.Blazor.Diagrams", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\Blazor.Diagrams.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj": {"projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components": {"target": "Package", "version": "(, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "Project dependency Microsoft.AspNetCore.Components does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "libraryId": "Microsoft.AspNetCore.Components", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "Project dependency Microsoft.AspNetCore.Components.Web does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "libraryId": "Microsoft.AspNetCore.Components.Web", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency Nanoid. Nanoid 1.0.0 was resolved instead.", "libraryId": "Nanoid", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}, {"code": "NU1602", "level": "Warning", "warningLevel": 1, "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency SvgPathProperties. SvgPathProperties 1.0.0 was resolved instead.", "libraryId": "SvgPathProperties", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}]}