{"Version": 1, "Hash": "rT5UfalDmgg1E1odzjHSm12pNLlCbPkiPxSpRzyT4uA=", "Source": "Z.Blazor.Diagrams", "BasePath": "_content/Z.Blazor.Diagrams", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Z.Blazor.Diagrams\\wwwroot", "Source": "Z.Blazor.Diagrams", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "Pattern": "**"}], "Assets": [{"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint=6pwzqlbbfs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhqk7gbln5", "Integrity": "7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "FileLength": 680, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint=c5cp0u3gkb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9r2jwh9rj", "Integrity": "g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.js", "FileLength": 651, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint=kr4r5y5l5h}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "469wz5lhr2", "Integrity": "bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.css", "FileLength": 838, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint=9j2o0uhpet}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yjpzo0a57p", "Integrity": "HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.css", "FileLength": 510, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 93, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.min.css", "FileLength": 418, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.Blazor.Diagrams#[.{fingerprint=wxhkjam3jz}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s3jxocm8qv", "Integrity": "+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "FileLength": 93, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.min.js", "FileLength": 520, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.<PERSON>lazor.Diagrams#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "wxhkjam3jz", "Integrity": "1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "FileLength": 81, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "Z.<PERSON>.Diagrams#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "wxhkjam3jz", "Integrity": "1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "FileLength": 81, "LastWriteTime": "2025-06-27T14:37:31+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kr4r5y5l5h", "Integrity": "IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.css", "FileLength": 3445, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "default.styles.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pwzqlbbfs", "Integrity": "tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\default.styles.min.css", "FileLength": 2533, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c5cp0u3gkb", "Integrity": "QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.js", "FileLength": 2034, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.min.js", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "script.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u872bpsf3j", "Integrity": "LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\script.min.js", "FileLength": 1071, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9j2o0uhpet", "Integrity": "K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.css", "FileLength": 1939, "LastWriteTime": "2025-06-27T14:31:03+00:00"}, {"Identity": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.min.css", "SourceId": "Z.Blazor.Diagrams", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\", "BasePath": "_content/Z.Blazor.Diagrams", "RelativePath": "style.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjpcwcpl0m", "Integrity": "UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\style.min.css", "FileLength": 1327, "LastWriteTime": "2025-06-27T14:31:03+00:00"}], "Endpoints": [{"Route": "default.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}]}, {"Route": "default.styles.kr4r5y5l5h.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "label", "Value": "default.styles.css"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.kr4r5y5l5h.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "label", "Value": "default.styles.css"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "default.styles.kr4r5y5l5h.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\8444umeufy-kr4r5y5l5h.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "label", "Value": "default.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}]}, {"Route": "default.styles.min.6pwzqlbbfs.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "label", "Value": "default.styles.min.css"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.6pwzqlbbfs.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "label", "Value": "default.styles.min.css"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.6pwzqlbbfs.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "label", "Value": "default.styles.min.css.gz"}, {"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}]}, {"Route": "default.styles.min.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "default.styles.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\2jdcekzucn-6pwzqlbbfs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}]}, {"Route": "script.c5cp0u3gkb.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "label", "Value": "script.js"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.c5cp0u3gkb.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "label", "Value": "script.js"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.c5cp0u3gkb.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "label", "Value": "script.js.gz"}, {"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}]}, {"Route": "script.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "script.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\6ox0u8yf04-c5cp0u3gkb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}]}, {"Route": "script.min.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "script.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "script.min.js"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.u872bpsf3j.js", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "script.min.js"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "script.min.u872bpsf3j.js.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\xbbnlhu4ft-u872bpsf3j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "label", "Value": "script.min.js.gz"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "style.9j2o0uhpet.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "label", "Value": "style.css"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.9j2o0uhpet.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "label", "Value": "style.css"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.9j2o0uhpet.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "label", "Value": "style.css.gz"}, {"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}]}, {"Route": "style.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "style.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\h5pe2zzrsc-9j2o0uhpet.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}]}, {"Route": "style.min.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "style.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "style.min.css"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.kjpcwcpl0m.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\wwwroot\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "style.min.css"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "style.min.kjpcwcpl0m.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\oyfobpjr5u-kjpcwcpl0m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "label", "Value": "style.min.css.gz"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "Z.Blazor.Diagrams.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Z.Blazor.Diagrams.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\kaqbb8qfo0-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Z.Blazor.Diagrams.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "Z.Blazor.Diagrams.wxhkjam3jz.styles.css.gz", "AssetFile": "D:\\projects\\MudBlazor\\Blazor.Diagrams-vba-b20250627\\src\\Blazor.Diagrams\\obj\\Debug\\net9.0\\compressed\\pno1t843y7-wxhkjam3jz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "label", "Value": "Z.Blazor.Diagrams.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}]}