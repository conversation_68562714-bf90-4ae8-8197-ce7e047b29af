using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using DiagramEditor.Data;
using DiagramEditor.Services;
using MudBlazor.Services;
using Microsoft.EntityFrameworkCore;
using Blazor.Diagrams.Persistence;
using Blazor.Diagrams.Persistence.Extensions;
using Blazor.Diagrams.Core.Persistence;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// Add MudBlazor services
builder.Services.AddMudServices();

// Add diagram persistence services with SQLite
builder.Services.AddDiagramPersistence(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection") ?? "Data Source=diagrams.db"));

// Add custom services
builder.Services.AddScoped<DiagramService>();
builder.Services.AddScoped<IDiagramRepository, DiagramRepository>();

builder.Services.AddSingleton<WeatherForecastService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();

app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

app.Run();
