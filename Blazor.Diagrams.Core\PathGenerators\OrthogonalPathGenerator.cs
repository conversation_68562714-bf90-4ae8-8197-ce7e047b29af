using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models.Base;
using SvgPathProperties;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Blazor.Diagrams.Core.PathGenerators;

/// <summary>
/// Path generator specifically designed for orthogonal links with crossing gap support
/// </summary>
public class OrthogonalPathGenerator : PathGenerator
{
    private readonly double _gapSize;
    private readonly bool _enableCrossingGaps;

    public OrthogonalPathGenerator(double gapSize = 8d, bool enableCrossingGaps = true)
    {
        _gapSize = gapSize;
        _enableCrossingGaps = enableCrossingGaps;
    }

    public override PathGeneratorResult GetResult(Diagram diagram, BaseLinkModel link, Point[] route, Point source, Point target)
    {
        route = ConcatRouteAndSourceAndTarget(route, source, target);

        if (route.Length < 2)
            return new PathGeneratorResult(new SvgPath(), Array.Empty<SvgPath>());

        double? sourceAngle = null;
        double? targetAngle = null;

        if (link.SourceMarker != null)
        {
            sourceAngle = AdjustRouteForSourceMarker(route, link.SourceMarker.Width);
        }

        if (link.TargetMarker != null)
        {
            targetAngle = AdjustRouteForTargetMarker(route, link.TargetMarker.Width);
        }

        // Find crossings with other orthogonal links if gap feature is enabled
        var crossings = _enableCrossingGaps ? FindLinkCrossings(diagram, link, route) : new List<LinkCrossing>();

        // Generate path with gaps at crossings
        var pathResult = GeneratePathWithGaps(route, crossings);

        return new PathGeneratorResult(pathResult.fullPath, pathResult.paths, sourceAngle, route[0], targetAngle, route[^1]);
    }

    /// <summary>
    /// Finds crossings between the current link route and other orthogonal links
    /// </summary>
    private List<LinkCrossing> FindLinkCrossings(Diagram diagram, BaseLinkModel currentLink, Point[] route)
    {
        var crossings = new List<LinkCrossing>();

        foreach (var otherLink in diagram.Links)
        {
            if (otherLink == currentLink || otherLink.Route == null || otherLink.Route.Length < 2)
                continue;

            // Only consider orthogonal links (straight segments)
            if (!IsOrthogonalRoute(otherLink.Route))
                continue;

            // Find intersections between route segments
            for (int i = 0; i < route.Length - 1; i++)
            {
                var currentSegment = new Line(route[i], route[i + 1]);
                
                for (int j = 0; j < otherLink.Route.Length - 1; j++)
                {
                    var otherSegment = new Line(otherLink.Route[j], otherLink.Route[j + 1]);
                    var intersection = currentSegment.GetIntersection(otherSegment);

                    if (intersection != null)
                    {
                        // Determine which link should have the gap (newer link gets the gap)
                        var shouldCreateGap = ShouldCreateGapForLink(currentLink, otherLink, currentSegment, otherSegment);
                        
                        if (shouldCreateGap)
                        {
                            crossings.Add(new LinkCrossing
                            {
                                Point = intersection,
                                SegmentIndex = i,
                                IsVerticalSegment = Math.Abs(currentSegment.Start.X - currentSegment.End.X) < 0.1,
                                OtherLinkIsVertical = Math.Abs(otherSegment.Start.X - otherSegment.End.X) < 0.1
                            });
                        }
                    }
                }
            }
        }

        return crossings.OrderBy(c => c.SegmentIndex).ToList();
    }

    /// <summary>
    /// Determines if the current link should create a gap when crossing another link
    /// </summary>
    private bool ShouldCreateGapForLink(BaseLinkModel currentLink, BaseLinkModel otherLink, Line currentSegment, Line otherSegment)
    {
        // Create gap if current segment is horizontal and other is vertical
        var currentIsHorizontal = Math.Abs(currentSegment.Start.Y - currentSegment.End.Y) < 0.1;
        var otherIsVertical = Math.Abs(otherSegment.Start.X - otherSegment.End.X) < 0.1;
        
        return currentIsHorizontal && otherIsVertical;
    }

    /// <summary>
    /// Checks if a route consists of orthogonal (horizontal/vertical) segments
    /// </summary>
    private bool IsOrthogonalRoute(Point[] route)
    {
        for (int i = 0; i < route.Length - 1; i++)
        {
            var segment = new Line(route[i], route[i + 1]);
            var isHorizontal = Math.Abs(segment.Start.Y - segment.End.Y) < 0.1;
            var isVertical = Math.Abs(segment.Start.X - segment.End.X) < 0.1;
            
            if (!isHorizontal && !isVertical)
                return false;
        }
        return true;
    }

    /// <summary>
    /// Generates SVG path with gaps at crossing points
    /// </summary>
    private (SvgPath fullPath, SvgPath[] paths) GeneratePathWithGaps(Point[] route, List<LinkCrossing> crossings)
    {
        var fullPath = new SvgPath();
        var pathSegments = new List<SvgPath>();

        if (route.Length == 0)
            return (fullPath, pathSegments.ToArray());

        fullPath.AddMoveTo(route[0].X, route[0].Y);
        var currentPath = new SvgPath().AddMoveTo(route[0].X, route[0].Y);

        for (int i = 0; i < route.Length - 1; i++)
        {
            var segmentStart = route[i];
            var segmentEnd = route[i + 1];
            var segmentCrossings = crossings.Where(c => c.SegmentIndex == i).OrderBy(c => 
                c.IsVerticalSegment ? c.Point.Y : c.Point.X).ToList();

            if (segmentCrossings.Count == 0)
            {
                // No crossings, draw straight line
                fullPath.AddLineTo(segmentEnd.X, segmentEnd.Y);
                currentPath.AddLineTo(segmentEnd.X, segmentEnd.Y);
            }
            else
            {
                // Draw segment with gaps at crossings
                var lastPoint = segmentStart;

                foreach (var crossing in segmentCrossings)
                {
                    var gapStart = CalculateGapStart(lastPoint, crossing.Point, crossing.IsVerticalSegment);
                    var gapEnd = CalculateGapEnd(crossing.Point, segmentEnd, crossing.IsVerticalSegment);

                    // Draw line to gap start
                    if (lastPoint.DistanceTo(gapStart) > 0.1)
                    {
                        fullPath.AddLineTo(gapStart.X, gapStart.Y);
                        currentPath.AddLineTo(gapStart.X, gapStart.Y);
                    }

                    // End current path segment and start new one after gap
                    pathSegments.Add(currentPath);
                    currentPath = new SvgPath().AddMoveTo(gapEnd.X, gapEnd.Y);
                    fullPath.AddMoveTo(gapEnd.X, gapEnd.Y);

                    lastPoint = gapEnd;
                }

                // Draw remaining segment
                if (lastPoint.DistanceTo(segmentEnd) > 0.1)
                {
                    fullPath.AddLineTo(segmentEnd.X, segmentEnd.Y);
                    currentPath.AddLineTo(segmentEnd.X, segmentEnd.Y);
                }
            }
        }

        pathSegments.Add(currentPath);
        return (fullPath, pathSegments.ToArray());
    }

    /// <summary>
    /// Calculates the start point of a gap before a crossing
    /// </summary>
    private Point CalculateGapStart(Point segmentStart, Point crossingPoint, bool isVerticalSegment)
    {
        var halfGap = _gapSize / 2;
        
        if (isVerticalSegment)
        {
            var direction = crossingPoint.Y > segmentStart.Y ? -1 : 1;
            return new Point(crossingPoint.X, crossingPoint.Y + direction * halfGap);
        }
        else
        {
            var direction = crossingPoint.X > segmentStart.X ? -1 : 1;
            return new Point(crossingPoint.X + direction * halfGap, crossingPoint.Y);
        }
    }

    /// <summary>
    /// Calculates the end point of a gap after a crossing
    /// </summary>
    private Point CalculateGapEnd(Point crossingPoint, Point segmentEnd, bool isVerticalSegment)
    {
        var halfGap = _gapSize / 2;
        
        if (isVerticalSegment)
        {
            var direction = segmentEnd.Y > crossingPoint.Y ? 1 : -1;
            return new Point(crossingPoint.X, crossingPoint.Y + direction * halfGap);
        }
        else
        {
            var direction = segmentEnd.X > crossingPoint.X ? 1 : -1;
            return new Point(crossingPoint.X + direction * halfGap, crossingPoint.Y);
        }
    }

    /// <summary>
    /// Represents a crossing between two orthogonal links
    /// </summary>
    private class LinkCrossing
    {
        public Point Point { get; set; } = Point.Zero;
        public int SegmentIndex { get; set; }
        public bool IsVerticalSegment { get; set; }
        public bool OtherLinkIsVertical { get; set; }
    }
}
