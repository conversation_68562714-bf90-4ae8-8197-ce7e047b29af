﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/default.styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IDKDuKfRCVCXOH3f/Z\u002BNueVf5u\u002B0YSUCGOLYdG\u002BZYf8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3445"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022IDKDuKfRCVCXOH3f/Z\u002BNueVf5u\u002B0YSUCGOLYdG\u002BZYf8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kr4r5y5l5h"},{"Name":"integrity","Value":"sha256-IDKDuKfRCVCXOH3f/Z\u002BNueVf5u\u002B0YSUCGOLYdG\u002BZYf8="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/default.styles.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3445"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022IDKDuKfRCVCXOH3f/Z\u002BNueVf5u\u002B0YSUCGOLYdG\u002BZYf8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6pwzqlbbfs"},{"Name":"integrity","Value":"sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/default.styles.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2533"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/default.styles.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2533"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c5cp0u3gkb"},{"Name":"integrity","Value":"sha256-QI5d3jQ5r735qncQ4geb3Y\u002BzDoaSz75G44z9f4XpdbY="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/script.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2034"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QI5d3jQ5r735qncQ4geb3Y\u002BzDoaSz75G44z9f4XpdbY=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/script.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QI5d3jQ5r735qncQ4geb3Y\u002BzDoaSz75G44z9f4XpdbY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2034"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QI5d3jQ5r735qncQ4geb3Y\u002BzDoaSz75G44z9f4XpdbY=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/script.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u872bpsf3j"},{"Name":"integrity","Value":"sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/script.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9j2o0uhpet"},{"Name":"integrity","Value":"sha256-K1X5IFVjQT\u002BBsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/style.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1939"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K1X5IFVjQT\u002BBsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/style.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K1X5IFVjQT\u002BBsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1939"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K1X5IFVjQT\u002BBsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/style.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UKzIp\u002BVqUElrrWqYXITbK2mVVp6d5hx2LP\u002BpnMfozLA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1327"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UKzIp\u002BVqUElrrWqYXITbK2mVVp6d5hx2LP\u002BpnMfozLA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kjpcwcpl0m"},{"Name":"integrity","Value":"sha256-UKzIp\u002BVqUElrrWqYXITbK2mVVp6d5hx2LP\u002BpnMfozLA="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/style.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1327"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UKzIp\u002BVqUElrrWqYXITbK2mVVp6d5hx2LP\u002BpnMfozLA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:31:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1BJ1LG\u002BGEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"81"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00221BJ1LG\u002BGEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:37:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wxhkjam3jz"},{"Name":"integrity","Value":"sha256-1BJ1LG\u002BGEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="},{"Name":"label","Value":"_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"81"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00221BJ1LG\u002BGEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\u0022"},{"Name":"Last-Modified","Value":"Fri, 27 Jun 2025 14:37:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>