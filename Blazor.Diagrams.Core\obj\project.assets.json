{"version": 3, "targets": {"net6.0": {"Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}}, "net7.0": {"Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}}, "net8.0": {"Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}}, "net9.0": {"Nanoid/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nanoid.dll": {"related": ".xml"}}}, "SvgPathProperties/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/SvgPathProperties.dll": {}}, "runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {}}}}}, "libraries": {"Nanoid/1.0.0": {"sha512": "HPFfNLCehToYlmYz+8wJKwl5EK9E/HJ8Jv2JUa3zLlKkuo9xLJF1w0Zdb1c5bee8yRj4W71Xsc10TiB0HUxdrA==", "type": "package", "path": "nanoid/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Nanoid.dll", "lib/netstandard2.0/Nanoid.xml", "nanoid.1.0.0.nupkg.sha512", "nanoid.nuspec"]}, "SvgPathProperties/1.0.0": {"sha512": "vRoBafA/voS5oy2MxM4CU4cz0SDAl+5pk1MA4botTW9yyI80SF0gyO/tBfPEQ+NT0OrMdSAOMJv0brfBfR8wyw==", "type": "package", "path": "svgpathproperties/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SvgPathProperties.dll", "svgpathproperties.1.0.0.nupkg.sha512", "svgpathproperties.nuspec"]}}, "projectFileDependencyGroups": {"net6.0": ["Nanoid", "SvgPathProperties"], "net7.0": ["Nanoid", "SvgPathProperties"], "net8.0": ["Nanoid", "SvgPathProperties"], "net9.0": ["Nanoid", "SvgPathProperties"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "3.0.3", "restore": {"projectUniqueName": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "projectName": "Z.Blazor.Diagrams.Core", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net7.0", "net8.0", "net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Nanoid": {"target": "Package", "version": "(, )"}, "SvgPathProperties": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Nanoid": {"target": "Package", "version": "(, )"}, "SvgPathProperties": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Nanoid": {"target": "Package", "version": "(, )"}, "SvgPathProperties": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0": {"targetAlias": "net9.0", "dependencies": {"Nanoid": {"target": "Package", "version": "(, )"}, "SvgPathProperties": {"target": "Package", "version": "(, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "Project dependency Nanoid does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "libraryId": "Nanoid", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1604", "level": "Warning", "warningLevel": 1, "message": "Project dependency SvgPathProperties does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "libraryId": "SvgPathProperties", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}]}