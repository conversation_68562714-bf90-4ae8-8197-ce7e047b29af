is_global = true
build_property.TargetFramework = net7.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Blazor.Diagrams.Algorithms
build_property.ProjectDir = D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Algorithms\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 7.0
build_property.EnableCodeStyleSeverity = 
