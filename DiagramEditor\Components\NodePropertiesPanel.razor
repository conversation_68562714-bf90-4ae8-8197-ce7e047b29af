@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Geometry

<MudDrawer @bind-Open="IsOpen" 
           Anchor="Anchor.Right" 
           Elevation="2" 
           Variant="DrawerVariant.Temporary"
           Width="350px">
    <MudDrawerHeader>
        <MudText Typo="Typo.h6">Propriedades do Nó</MudText>
        <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="Close" />
    </MudDrawerHeader>
    
    <MudDrawerContainer>
        @if (SelectedNode != null)
        {
            <MudContainer Class="pa-4">
                <MudStack Spacing="3">
                    <!-- Informações Básicas -->
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Informações Básicas</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="2">
                                <MudTextField @bind-Value="NodeTitle" 
                                            Label="Título" 
                                            Variant="Variant.Outlined"
                                            OnBlur="UpdateNodeTitle" />
                                
                                <MudTextField @bind-Value="NodeId" 
                                            Label="ID" 
                                            Variant="Variant.Outlined"
                                            ReadOnly="true" />
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- Posição e Tamanho -->
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Posição e Tamanho</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="2">
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="NodeX" 
                                                       Label="X" 
                                                       Variant="Variant.Outlined"
                                                       OnBlur="UpdateNodePosition" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="NodeY" 
                                                       Label="Y" 
                                                       Variant="Variant.Outlined"
                                                       OnBlur="UpdateNodePosition" />
                                    </MudItem>
                                </MudGrid>
                                
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="NodeWidth" 
                                                       Label="Largura" 
                                                       Variant="Variant.Outlined"
                                                       OnBlur="UpdateNodeSize" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="NodeHeight" 
                                                       Label="Altura" 
                                                       Variant="Variant.Outlined"
                                                       OnBlur="UpdateNodeSize" />
                                    </MudItem>
                                </MudGrid>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- Aparência -->
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Aparência</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="2">
                                <MudColorPicker @bind-Value="NodeColor" 
                                              Label="Cor de Fundo"
                                              OnColorChanged="UpdateNodeColor" />
                                
                                <MudColorPicker @bind-Value="NodeBorderColor" 
                                              Label="Cor da Borda"
                                              OnColorChanged="UpdateNodeBorderColor" />
                                
                                <MudSlider @bind-Value="NodeBorderWidth"
                                         Min="0"
                                         Max="10"
                                         Step="1"
                                         ValueLabel="true"
                                         OnChange="UpdateNodeBorderWidth">
                                    Espessura da Borda: @NodeBorderWidth px
                                </MudSlider>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- Portas -->
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Portas de Conexão</MudText>
                            </CardHeaderContent>
                            <CardHeaderActions>
                                <MudIconButton Icon="@Icons.Material.Filled.Add" 
                                             OnClick="AddPort" 
                                             Size="MudBlazor.Size.Small" />
                            </CardHeaderActions>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="1">
                                @foreach (var port in NodePorts)
                                {
                                    <MudChip Text="@($"Porta {port.Alignment}")" 
                                           OnClose="() => RemovePort(port)"
                                           CloseIcon="@Icons.Material.Filled.Close" />
                                }
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <!-- Ações -->
                    <MudStack Row Spacing="2">
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 OnClick="ApplyChanges"
                                 FullWidth="true">
                            Aplicar Alterações
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                 Color="Color.Error" 
                                 OnClick="DeleteNode"
                                 FullWidth="true">
                            Excluir Nó
                        </MudButton>
                    </MudStack>
                </MudStack>
            </MudContainer>
        }
        else
        {
            <MudContainer Class="pa-4">
                <MudAlert Severity="Severity.Info">
                    Selecione um nó para editar suas propriedades.
                </MudAlert>
            </MudContainer>
        }
    </MudDrawerContainer>
</MudDrawer>

@code {
    [Parameter] public bool IsOpen { get; set; }
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }
    [Parameter] public NodeModel? SelectedNode { get; set; }
    [Parameter] public EventCallback OnNodeUpdated { get; set; }
    [Parameter] public EventCallback OnNodeDeleted { get; set; }

    private string NodeTitle = "";
    private string NodeId = "";
    private double NodeX = 0;
    private double NodeY = 0;
    private double NodeWidth = 100;
    private double NodeHeight = 50;
    private MudBlazor.Utilities.MudColor NodeColor = "#2196F3";
    private MudBlazor.Utilities.MudColor NodeBorderColor = "#1976D2";
    private int NodeBorderWidth = 2;
    private List<PortModel> NodePorts = new();

    protected override void OnParametersSet()
    {
        if (SelectedNode != null)
        {
            LoadNodeProperties();
        }
    }

    private void LoadNodeProperties()
    {
        if (SelectedNode == null) return;

        NodeTitle = SelectedNode.Title ?? "";
        NodeId = SelectedNode.Id;
        NodeX = SelectedNode.Position.X;
        NodeY = SelectedNode.Position.Y;
        NodeWidth = SelectedNode.Size?.Width ?? 100;
        NodeHeight = SelectedNode.Size?.Height ?? 50;
        NodePorts = SelectedNode.Ports.ToList();
    }

    private async Task Close()
    {
        IsOpen = false;
        await IsOpenChanged.InvokeAsync(IsOpen);
    }

    private void UpdateNodeTitle()
    {
        if (SelectedNode != null)
        {
            SelectedNode.Title = NodeTitle;
            OnNodeUpdated.InvokeAsync();
        }
    }

    private void UpdateNodePosition()
    {
        if (SelectedNode != null)
        {
            SelectedNode.SetPosition(NodeX, NodeY);
            OnNodeUpdated.InvokeAsync();
        }
    }

    private void UpdateNodeSize()
    {
        if (SelectedNode != null)
        {
            SelectedNode.Size = new Blazor.Diagrams.Core.Geometry.Size(NodeWidth, NodeHeight);
            OnNodeUpdated.InvokeAsync();
        }
    }

    private void UpdateNodeColor(MudBlazor.Utilities.MudColor color)
    {
        NodeColor = color;
        OnNodeUpdated.InvokeAsync();
    }

    private void UpdateNodeBorderColor(MudBlazor.Utilities.MudColor color)
    {
        NodeBorderColor = color;
        OnNodeUpdated.InvokeAsync();
    }

    private void UpdateNodeBorderWidth(int width)
    {
        NodeBorderWidth = width;
        OnNodeUpdated.InvokeAsync();
    }

    private void AddPort()
    {
        if (SelectedNode != null)
        {
            SelectedNode.AddPort(PortAlignment.Right);
            NodePorts = SelectedNode.Ports.ToList();
            OnNodeUpdated.InvokeAsync();
        }
    }

    private void RemovePort(PortModel port)
    {
        if (SelectedNode != null)
        {
            SelectedNode.RemovePort(port);
            NodePorts = SelectedNode.Ports.ToList();
            OnNodeUpdated.InvokeAsync();
        }
    }

    private void ApplyChanges()
    {
        UpdateNodeTitle();
        UpdateNodePosition();
        UpdateNodeSize();
        OnNodeUpdated.InvokeAsync();
    }

    private void DeleteNode()
    {
        OnNodeDeleted.InvokeAsync();
        Close();
    }
}
