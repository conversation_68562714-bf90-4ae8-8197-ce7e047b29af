@page "/diagram-editor"
@using Blazor.Diagrams.Core.PathGenerators
@using Blazor.Diagrams.Core.Routers
@using Blazor.Diagrams.Core.Persistence
@inject IDiagramPersistenceService DiagramPersistenceService
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="d-flex flex-column" Style="height: calc(100vh - 150px);">
    <MudPaper Elevation="3" Class="pa-3 mb-3">
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="AddNode" StartIcon="@Icons.Material.Filled.Add" Class="mr-2">Adicionar <PERSON>ó</MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="string" Label="Tipo de Link" Value="_selectedLinkType" ValueChanged="OnLinkTypeChanged">
                    <MudSelectItem Value="@("straight")">Reto</MudSelectItem>
                    <MudSelectItem Value="@("smooth")">Suave</MudSelectItem>
                    <MudSelectItem Value="@("orthogonal")">Ortogonal</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="SaveDiagram" StartIcon="@Icons.Material.Filled.Save" Class="mr-2">Salvar</MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Info" OnClick="LoadDiagram" StartIcon="@Icons.Material.Filled.Upload">Carregar</MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <MudPaper Elevation="3" Class="flex-grow-1 position-relative">
        <DiagramCanvas @ref="_diagramCanvas" BlazorDiagram="_blazorDiagram" Class="position-absolute" Style="top: 0; left: 0; right: 0; bottom: 0;" />
    </MudPaper>

    @if (_selectedNode != null)
    {
        <MudPaper Elevation="3" Class="pa-3 mt-3">
            <MudText Typo="Typo.h6">Propriedades do Nó</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField @bind-Value="_selectedNode.Title" Label="Título" Immediate="true" OnBlur="UpdateNode" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedNodeColor" Label="Cor de Fundo" Immediate="true" OnBlur="UpdateNodeColor" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedNodeBorderColor" Label="Cor da Borda" Immediate="true" OnBlur="UpdateNodeBorderColor" />
                </MudItem>
            </MudGrid>
        </MudPaper>
    }

    @if (_selectedLink != null)
    {
        <MudPaper Elevation="3" Class="pa-3 mt-3">
            <MudText Typo="Typo.h6">Propriedades do Link</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedLinkColor" Label="Cor da Linha" Immediate="true" OnBlur="UpdateLinkColor" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudSlider @bind-Value="_selectedLinkWidth" Min="1" Max="10" Step="1" Label="Espessura da Linha" Immediate="true" ValueLabel="true" OnBlur="UpdateLinkWidth" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudSelect T="string" Label="Marcador" Value="_selectedLinkMarker" ValueChanged="OnLinkMarkerChanged">
                        <MudSelectItem Value="@("none")">Nenhum</MudSelectItem>
                        <MudSelectItem Value="@("arrow")">Seta</MudSelectItem>
                        <MudSelectItem Value="@("circle")">Círculo</MudSelectItem>
                        <MudSelectItem Value="@("square")">Quadrado</MudSelectItem>
                    </MudSelect>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private BlazorDiagram _blazorDiagram;
    private DiagramCanvas _diagramCanvas;
    private string _selectedLinkType = "orthogonal";
    private string _selectedLinkMarker = "arrow";
    private NodeModel _selectedNode;
    private LinkModel _selectedLink;
    private string _selectedNodeColor = "#FFFFFF";
    private string _selectedNodeBorderColor = "#000000";
    private string _selectedLinkColor = "#000000";
    private double _selectedLinkWidth = 2;
    private string _currentDiagramId;

    protected override void OnInitialized()
    {
        var options = new BlazorDiagramOptions
        {
            Links = new BlazorDiagramLinkOptions
            {
                DefaultColor = "#000000",
                DefaultSelectedColor = "#3D8BFD",
                DefaultRouter = new NormalRouter(),
                DefaultPathGenerator = new OrthogonalPathGenerator()
            },
            Zoom = new BlazorDiagramZoomOptions
            {
                Enabled = true,
                Inverse = false,
                ScaleFactor = 1.2
            }
        };

        _blazorDiagram = new BlazorDiagram(options);
        _blazorDiagram.SelectionChanged += OnSelectionChanged;
        
        // Registrar o componente personalizado para os nós
        _blazorDiagram.RegisterComponent<NodeModel, CustomNodeWidget>();
    }

    private void OnSelectionChanged(IEnumerable<SelectableModel> models)
    {
        _selectedNode = null;
        _selectedLink = null;

        var selectedModel = models.FirstOrDefault();
        if (selectedModel is NodeModel node)
        {
            _selectedNode = node;
            _selectedNodeColor = node.Data?.TryGetValue("BackgroundColor", out var bgColor) == true ? bgColor.ToString() : "#FFFFFF";
            _selectedNodeBorderColor = node.Data?.TryGetValue("BorderColor", out var borderColor) == true ? borderColor.ToString() : "#000000";
        }
        else if (selectedModel is LinkModel link)
        {
            _selectedLink = link;
            _selectedLinkColor = link.Color ?? "#000000";
            _selectedLinkWidth = link.Width;
            _selectedLinkMarker = link.TargetMarker == LinkMarker.Arrow ? "arrow" :
                                link.TargetMarker == LinkMarker.Circle ? "circle" :
                                link.TargetMarker == LinkMarker.Square ? "square" : "none";
        }

        StateHasChanged();
    }

    private void AddNode()
    {
        var node = new NodeModel(new Point(100 + _blazorDiagram.Nodes.Count * 50, 100 + _blazorDiagram.Nodes.Count * 30))
        {
            Title = $"Nó {_blazorDiagram.Nodes.Count + 1}"
        };

        if (node.Data == null)
            node.Data = new Dictionary<string, object>();

        node.Data["BackgroundColor"] = "#FFFFFF";
        node.Data["BorderColor"] = "#000000";

        // Adicionar portas aos quatro lados do nó
        node.AddPort(PortAlignment.Top);
        node.AddPort(PortAlignment.Right);
        node.AddPort(PortAlignment.Bottom);
        node.AddPort(PortAlignment.Left);

        _blazorDiagram.AddNode(node);
    }

    private void UpdateNode()
    {
        if (_selectedNode != null)
        {
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateNodeColor()
    {
        if (_selectedNode != null)
        {
            if (_selectedNode.Data == null)
                _selectedNode.Data = new Dictionary<string, object>();

            _selectedNode.Data["BackgroundColor"] = _selectedNodeColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateNodeBorderColor()
    {
        if (_selectedNode != null)
        {
            if (_selectedNode.Data == null)
                _selectedNode.Data = new Dictionary<string, object>();

            _selectedNode.Data["BorderColor"] = _selectedNodeBorderColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateLinkColor()
    {
        if (_selectedLink != null)
        {
            _selectedLink.Color = _selectedLinkColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateLinkWidth()
    {
        if (_selectedLink != null)
        {
            _selectedLink.Width = _selectedLinkWidth;
            _blazorDiagram.Refresh();
        }
    }

    private void OnLinkTypeChanged(string value)
    {
        _selectedLinkType = value;

        switch (value)
        {
            case "straight":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new StraightPathGenerator();
                break;
            case "smooth":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new SmoothPathGenerator();
                break;
            case "orthogonal":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new OrthogonalPathGenerator();
                break;
        }

        _blazorDiagram.Refresh();
    }

    private void OnLinkMarkerChanged(string value)
    {
        _selectedLinkMarker = value;

        if (_selectedLink != null)
        {
            switch (value)
            {
                case "arrow":
                    _selectedLink.TargetMarker = LinkMarker.Arrow;
                    break;
                case "circle":
                    _selectedLink.TargetMarker = LinkMarker.Circle;
                    break;
                case "square":
                    _selectedLink.TargetMarker = LinkMarker.Square;
                    break;
                default:
                    _selectedLink.TargetMarker = null;
                    break;
            }

            _blazorDiagram.Refresh();
        }
    }

    private async Task SaveDiagram()
    {
        try
        {
            _currentDiagramId = await DiagramPersistenceService.SaveDiagramAsync(_blazorDiagram, "Meu Diagrama", "Diagrama criado com o editor");
            Snackbar.Add("Diagrama salvo com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDiagram()
    {
        if (string.IsNullOrEmpty(_currentDiagramId))
        {
            Snackbar.Add("Nenhum diagrama salvo para carregar.", Severity.Warning);
            return;
        }

        try
        {
            var diagram = await DiagramPersistenceService.LoadDiagramAsync(_currentDiagramId);
            if (diagram != null)
            {
                _blazorDiagram.Nodes.Clear();
                _blazorDiagram.Links.Clear();

                foreach (var node in diagram.Nodes)
                {
                    _blazorDiagram.AddNode(node);
                }

                foreach (var link in diagram.Links)
                {
                    _blazorDiagram.AddLink(link);
                }

                Snackbar.Add("Diagrama carregado com sucesso!", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar diagrama: {ex.Message}", Severity.Error);
        }
    }
}