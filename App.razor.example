<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <MudContainer MaxWidth="MaxWidth.Small" Class="d-flex align-center justify-center mud-height-full">
                <MudPaper Elevation="3" Class="pa-8 mud-width-full">
                    <MudText Typo="Typo.h4" Align="Align.Center" Class="mb-4">Página não encontrada</MudText>
                    <MudText Align="Align.Center">A página que você está procurando não existe ou foi movida.</MudText>
                    <div class="d-flex justify-center mt-6">
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Link="/" StartIcon="@Icons.Material.Filled.Home">Voltar para a página inicial</MudButton>
                    </div>
                </MudPaper>
            </MudContainer>
        </LayoutView>
    </NotFound>
</Router>