/* _content/DiagramEditor/Shared/MainLayout.razor.rz.scp.css */
.page[b-rjay8f6slb] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-rjay8f6slb] {
    flex: 1;
}

.sidebar[b-rjay8f6slb] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-rjay8f6slb] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-rjay8f6slb]  a, .top-row .btn-link[b-rjay8f6slb] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-rjay8f6slb] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-rjay8f6slb] {
        display: none;
    }

    .top-row.auth[b-rjay8f6slb] {
        justify-content: space-between;
    }

    .top-row a[b-rjay8f6slb], .top-row .btn-link[b-rjay8f6slb] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-rjay8f6slb] {
        flex-direction: row;
    }

    .sidebar[b-rjay8f6slb] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-rjay8f6slb] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-rjay8f6slb], article[b-rjay8f6slb] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* _content/DiagramEditor/Shared/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-cooobubskc] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-cooobubskc] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-cooobubskc] {
    font-size: 1.1rem;
}

.oi[b-cooobubskc] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-cooobubskc] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-cooobubskc] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-cooobubskc] {
        padding-bottom: 1rem;
    }

    .nav-item[b-cooobubskc]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-cooobubskc]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-cooobubskc]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-cooobubskc] {
        display: none;
    }

    .collapse[b-cooobubskc] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
    
    .nav-scrollable[b-cooobubskc] {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
