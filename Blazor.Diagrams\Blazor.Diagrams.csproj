﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <Nullable>enable</Nullable>
    <Authors>zH<PERSON><PERSON></Authors>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <AssemblyVersion>3.0.3</AssemblyVersion>
    <FileVersion>3.0.3</FileVersion>
    <RepositoryUrl>https://github.com/Blazor-Diagrams/Blazor.Diagrams</RepositoryUrl>
    <Description>A fully customizable and extensible all-purpose diagrams library for Blazor</Description>
    <Version>3.0.3</Version>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageTags>blazor diagrams diagramming svg drag</PackageTags>
    <PackageId>Z.Blazor.Diagrams</PackageId>
    <PackageProjectUrl>https://blazor-diagrams.zhaytam.com/</PackageProjectUrl>
    <Product>Z.Blazor.Diagrams</Product>
    <PackageIcon>ZBD.png</PackageIcon>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" />
  </ItemGroup>




  <ItemGroup>
    <ProjectReference Include="..\Blazor.Diagrams.Core\Blazor.Diagrams.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\ZBD.png">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <Target Name="TestWebCompiler" AfterTargets="BeforeBuild">
    <!-- Test if Excubo.WebCompiler is installed (recommended) -->
    <Exec Command="webcompiler -h" ContinueOnError="true" StandardOutputImportance="low" StandardErrorImportance="low" LogStandardErrorAsError="false" IgnoreExitCode="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
  </Target>

  <Target Name="CompileStaticAssets" AfterTargets="CoreCompile;TestWebCompiler" Condition="'$(ErrorCode)' == '0'">
    <Exec Command="webcompiler --zip disable -r wwwroot" StandardOutputImportance="high" StandardErrorImportance="high" />
  </Target>

</Project>
