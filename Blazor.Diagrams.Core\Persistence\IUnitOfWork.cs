using System;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Unit of Work pattern for managing transactions and repositories
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Repository for diagram entities
    /// </summary>
    IRepository<DiagramEntity> Diagrams { get; }
    
    /// <summary>
    /// Repository for node entities
    /// </summary>
    IRepository<NodeEntity> Nodes { get; }
    
    /// <summary>
    /// Repository for link entities
    /// </summary>
    IRepository<LinkEntity> Links { get; }
    
    /// <summary>
    /// Repository for port entities
    /// </summary>
    IRepository<PortEntity> Ports { get; }
    
    /// <summary>
    /// Repository for group entities
    /// </summary>
    IRepository<GroupEntity> Groups { get; }
    
    /// <summary>
    /// Begin a transaction
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Commit the current transaction
    /// </summary>
    Task CommitAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Rollback the current transaction
    /// </summary>
    Task RollbackAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Save all changes
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
