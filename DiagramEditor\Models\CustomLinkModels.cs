using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core.Anchors;

namespace DiagramEditor.Models;

/// <summary>
/// Link personalizado com estilos
/// </summary>
public class CustomLinkModel : LinkModel
{
    public CustomLinkModel(Anchor source, Anchor target) : base(source, target)
    {
        Color = "#666666";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
    }

    public CustomLinkModel(Model source, Model target) : base(source, target)
    {
        Color = "#666666";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
    }

    public new string Color { get; set; }
    public new double Width { get; set; }
    public new LinkMarker TargetMarker { get; set; }
    public new LinkMarker SourceMarker { get; set; } = LinkMarker.None;
    public string Style { get; set; } = "solid"; // solid, dashed, dotted
    public string Label { get; set; } = "";
    public bool Animated { get; set; } = false;
}

/// <summary>
/// Link de fluxo de dados
/// </summary>
public class DataFlowLinkModel : CustomLinkModel
{
    public DataFlowLinkModel(Anchor source, Anchor target) : base(source, target)
    {
        Color = "#2196F3";
        Width = 3;
        TargetMarker = LinkMarker.Arrow;
        Style = "solid";
        Label = "Dados";
    }

    public DataFlowLinkModel(Model source, Model target) : base(source, target)
    {
        Color = "#2196F3";
        Width = 3;
        TargetMarker = LinkMarker.Arrow;
        Style = "solid";
        Label = "Dados";
    }

    public string DataType { get; set; } = "Object";
    public bool IsAsync { get; set; } = false;
}

/// <summary>
/// Link de controle de fluxo
/// </summary>
public class ControlFlowLinkModel : CustomLinkModel
{
    public ControlFlowLinkModel(Anchor source, Anchor target) : base(source, target)
    {
        Color = "#FF5722";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
        Style = "dashed";
        Label = "Controle";
    }

    public ControlFlowLinkModel(Model source, Model target) : base(source, target)
    {
        Color = "#FF5722";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
        Style = "dashed";
        Label = "Controle";
    }

    public string Condition { get; set; } = "";
    public int Priority { get; set; } = 1;
}

/// <summary>
/// Link de dependência
/// </summary>
public class DependencyLinkModel : CustomLinkModel
{
    public DependencyLinkModel(Anchor source, Anchor target) : base(source, target)
    {
        Color = "#9C27B0";
        Width = 1;
        TargetMarker = LinkMarker.Arrow;
        Style = "dotted";
        Label = "Depende";
    }

    public DependencyLinkModel(Model source, Model target) : base(source, target)
    {
        Color = "#9C27B0";
        Width = 1;
        TargetMarker = LinkMarker.Arrow;
        Style = "dotted";
        Label = "Depende";
    }

    public DependencyType Type { get; set; } = DependencyType.Required;
    public new string Version { get; set; } = "";
}

/// <summary>
/// Link bidirecional
/// </summary>
public class BidirectionalLinkModel : CustomLinkModel
{
    public BidirectionalLinkModel(Anchor source, Anchor target) : base(source, target)
    {
        Color = "#4CAF50";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
        SourceMarker = LinkMarker.Arrow;
        Style = "solid";
        Label = "Bidirecional";
    }

    public BidirectionalLinkModel(Model source, Model target) : base(source, target)
    {
        Color = "#4CAF50";
        Width = 2;
        TargetMarker = LinkMarker.Arrow;
        SourceMarker = LinkMarker.Arrow;
        Style = "solid";
        Label = "Bidirecional";
    }

    public string ForwardLabel { get; set; } = "";
    public string BackwardLabel { get; set; } = "";
}

/// <summary>
/// Tipos de marcadores para links
/// </summary>
public enum LinkMarker
{
    None,
    Arrow,
    Circle,
    Diamond,
    Square
}

/// <summary>
/// Tipos de dependência
/// </summary>
public enum DependencyType
{
    Required,
    Optional,
    Weak,
    Strong
}

/// <summary>
/// Factory para criar links personalizados
/// </summary>
public static class LinkFactory
{
    public static CustomLinkModel CreateLink(LinkType type, Anchor source, Anchor target)
    {
        return type switch
        {
            LinkType.DataFlow => new DataFlowLinkModel(source, target),
            LinkType.ControlFlow => new ControlFlowLinkModel(source, target),
            LinkType.Dependency => new DependencyLinkModel(source, target),
            LinkType.Bidirectional => new BidirectionalLinkModel(source, target),
            _ => new CustomLinkModel(source, target)
        };
    }

    public static CustomLinkModel CreateLink(LinkType type, Model source, Model target)
    {
        return type switch
        {
            LinkType.DataFlow => new DataFlowLinkModel(source, target),
            LinkType.ControlFlow => new ControlFlowLinkModel(source, target),
            LinkType.Dependency => new DependencyLinkModel(source, target),
            LinkType.Bidirectional => new BidirectionalLinkModel(source, target),
            _ => new CustomLinkModel(source, target)
        };
    }
}

/// <summary>
/// Tipos de links disponíveis
/// </summary>
public enum LinkType
{
    Default,
    DataFlow,
    ControlFlow,
    Dependency,
    Bidirectional
}
