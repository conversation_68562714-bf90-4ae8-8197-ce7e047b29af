<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <Authors>zHaytam</Authors>
    <Description>Entity Framework persistence layer for Blazor.Diagrams</Description>
    <AssemblyVersion>3.0.3</AssemblyVersion>
    <FileVersion>3.0.3</FileVersion>
    <RepositoryUrl>https://github.com/Blazor-Diagrams/Blazor.Diagrams</RepositoryUrl>
    <Version>3.0.3</Version>
    <PackageId>Z.Blazor.Diagrams.Persistence</PackageId>
    <PackageTags>blazor diagrams persistence entityframework database</PackageTags>
    <Product>Z.Blazor.Diagrams.Persistence</Product>
    <PackageIcon>ZBD.png</PackageIcon>
    <PackageProjectUrl>https://blazor-diagrams.zhaytam.com/</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\..\ZBD.png">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Blazor.Diagrams.Core\Blazor.Diagrams.Core.csproj" />
  </ItemGroup>

</Project>
