﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Z.Blazor.Diagrams.Algorithms</id>
    <version>3.0.3</version>
    <authors>zHaytam</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <description>Algorithms for Z.Blazor.Diagrams</description>
    <tags>blazor diagrams diagramming svg drag algorithms layouts</tags>
    <repository type="git" url="https://github.com/zHaytam/Blazor.Diagrams" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net9.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Algorithms\bin\Debug\net6.0\Blazor.Diagrams.Algorithms.dll" target="lib\net6.0\Blazor.Diagrams.Algorithms.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Algorithms\bin\Debug\net7.0\Blazor.Diagrams.Algorithms.dll" target="lib\net7.0\Blazor.Diagrams.Algorithms.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Algorithms\bin\Debug\net8.0\Blazor.Diagrams.Algorithms.dll" target="lib\net8.0\Blazor.Diagrams.Algorithms.dll" />
    <file src="D:\projects\MudBlazor\BOS.Blazor.Diagrams\Blazor.Diagrams.Algorithms\bin\Debug\net9.0\Blazor.Diagrams.Algorithms.dll" target="lib\net9.0\Blazor.Diagrams.Algorithms.dll" />
  </files>
</package>