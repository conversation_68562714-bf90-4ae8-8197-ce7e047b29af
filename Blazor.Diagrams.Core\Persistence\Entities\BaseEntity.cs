using System;
using System.ComponentModel.DataAnnotations;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Base entity class with common persistence properties
/// </summary>
public abstract class BaseEntity : IPersistable
{
    [Key]
    public string Id { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    [Timestamp]
    public int Version { get; set; }
    
    /// <summary>
    /// Update the UpdatedAt timestamp
    /// </summary>
    public virtual void Touch()
    {
        UpdatedAt = DateTime.UtcNow;
    }
}
