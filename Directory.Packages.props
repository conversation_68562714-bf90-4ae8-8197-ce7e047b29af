<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Core Dependencies -->
    <PackageVersion Include="Nanoid" Version="3.1.0" />
    <PackageVersion Include="SvgPathProperties" Version="1.1.0" />
    <!-- ASP.NET Core Dependencies - using versions compatible with multiple frameworks -->
    <PackageVersion Include="Microsoft.AspNetCore.Components" Version="6.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.Web" Version="6.0.0" />
    <!-- Entity Framework Dependencies - using versions compatible with multiple frameworks -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="6.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.0" />
    <!-- Dependency Injection -->
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
    <!-- MudBlazor for UI -->
    <PackageVersion Include="MudBlazor" Version="6.11.2" />
    <!-- Testing Dependencies -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.6.1" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Moq" Version="4.20.69" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.SignalR.Client" Version="7.0.20" />
  </ItemGroup>
</Project>