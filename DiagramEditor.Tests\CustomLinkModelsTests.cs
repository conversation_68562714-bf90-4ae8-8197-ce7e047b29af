using DiagramEditor.Models;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Geometry;

namespace DiagramEditor.Tests;

public class CustomLinkModelsTests
{
    private readonly NodeModel _sourceNode;
    private readonly NodeModel _targetNode;

    public CustomLinkModelsTests()
    {
        _sourceNode = new NodeModel(new Point(0, 0));
        _targetNode = new NodeModel(new Point(100, 100));
    }

    [Fact]
    public void CustomLinkModel_Constructor_SetsCorrectDefaults()
    {
        // Act
        var link = new CustomLinkModel(_sourceNode, _targetNode);

        // Assert
        Assert.Equal("#666666", link.Color);
        Assert.Equal(2, link.Width);
        Assert.Equal(LinkMarker.Arrow, link.TargetMarker);
        Assert.Equal(LinkMarker.None, link.SourceMarker);
        Assert.Equal("solid", link.Style);
        Assert.Equal("", link.Label);
        Assert.False(link.Animated);
    }

    [Fact]
    public void DataFlowLinkModel_Constructor_SetsCorrectDefaults()
    {
        // Act
        var link = new DataFlowLinkModel(_sourceNode, _targetNode);

        // Assert
        Assert.Equal("#2196F3", link.Color);
        Assert.Equal(3, link.Width);
        Assert.Equal("solid", link.Style);
        Assert.Equal("Dados", link.Label);
        Assert.Equal("Object", link.DataType);
        Assert.False(link.IsAsync);
    }

    [Fact]
    public void ControlFlowLinkModel_Constructor_SetsCorrectDefaults()
    {
        // Act
        var link = new ControlFlowLinkModel(_sourceNode, _targetNode);

        // Assert
        Assert.Equal("#FF5722", link.Color);
        Assert.Equal(2, link.Width);
        Assert.Equal("dashed", link.Style);
        Assert.Equal("Controle", link.Label);
        Assert.Equal("", link.Condition);
        Assert.Equal(1, link.Priority);
    }

    [Fact]
    public void DependencyLinkModel_Constructor_SetsCorrectDefaults()
    {
        // Act
        var link = new DependencyLinkModel(_sourceNode, _targetNode);

        // Assert
        Assert.Equal("#9C27B0", link.Color);
        Assert.Equal(1, link.Width);
        Assert.Equal("dotted", link.Style);
        Assert.Equal("Depende", link.Label);
        Assert.Equal(DependencyType.Required, link.Type);
        Assert.Equal("", link.Version);
    }

    [Fact]
    public void BidirectionalLinkModel_Constructor_SetsCorrectDefaults()
    {
        // Act
        var link = new BidirectionalLinkModel(_sourceNode, _targetNode);

        // Assert
        Assert.Equal("#4CAF50", link.Color);
        Assert.Equal(2, link.Width);
        Assert.Equal(LinkMarker.Arrow, link.TargetMarker);
        Assert.Equal(LinkMarker.Arrow, link.SourceMarker);
        Assert.Equal("solid", link.Style);
        Assert.Equal("Bidirecional", link.Label);
        Assert.Equal("", link.ForwardLabel);
        Assert.Equal("", link.BackwardLabel);
    }

    [Theory]
    [InlineData(LinkType.Default, typeof(CustomLinkModel))]
    [InlineData(LinkType.DataFlow, typeof(DataFlowLinkModel))]
    [InlineData(LinkType.ControlFlow, typeof(ControlFlowLinkModel))]
    [InlineData(LinkType.Dependency, typeof(DependencyLinkModel))]
    [InlineData(LinkType.Bidirectional, typeof(BidirectionalLinkModel))]
    public void LinkFactory_CreateLink_ReturnsCorrectType(LinkType linkType, Type expectedType)
    {
        // Act
        var link = LinkFactory.CreateLink(linkType, _sourceNode, _targetNode);

        // Assert
        Assert.IsType(expectedType, link);
    }

    [Fact]
    public void CustomLinkModel_Properties_CanBeModified()
    {
        // Arrange
        var link = new CustomLinkModel(_sourceNode, _targetNode);

        // Act
        link.Color = "#FF0000";
        link.Width = 5;
        link.Style = "dashed";
        link.Label = "Test Label";
        link.Animated = true;
        link.SourceMarker = LinkMarker.Circle;

        // Assert
        Assert.Equal("#FF0000", link.Color);
        Assert.Equal(5, link.Width);
        Assert.Equal("dashed", link.Style);
        Assert.Equal("Test Label", link.Label);
        Assert.True(link.Animated);
        Assert.Equal(LinkMarker.Circle, link.SourceMarker);
    }

    [Fact]
    public void DataFlowLinkModel_SpecificProperties_CanBeModified()
    {
        // Arrange
        var link = new DataFlowLinkModel(_sourceNode, _targetNode);

        // Act
        link.DataType = "String";
        link.IsAsync = true;

        // Assert
        Assert.Equal("String", link.DataType);
        Assert.True(link.IsAsync);
    }

    [Fact]
    public void ControlFlowLinkModel_SpecificProperties_CanBeModified()
    {
        // Arrange
        var link = new ControlFlowLinkModel(_sourceNode, _targetNode);

        // Act
        link.Condition = "x > 0";
        link.Priority = 5;

        // Assert
        Assert.Equal("x > 0", link.Condition);
        Assert.Equal(5, link.Priority);
    }

    [Fact]
    public void DependencyLinkModel_SpecificProperties_CanBeModified()
    {
        // Arrange
        var link = new DependencyLinkModel(_sourceNode, _targetNode);

        // Act
        link.Type = DependencyType.Optional;
        link.Version = "1.0.0";

        // Assert
        Assert.Equal(DependencyType.Optional, link.Type);
        Assert.Equal("1.0.0", link.Version);
    }

    [Fact]
    public void BidirectionalLinkModel_SpecificProperties_CanBeModified()
    {
        // Arrange
        var link = new BidirectionalLinkModel(_sourceNode, _targetNode);

        // Act
        link.ForwardLabel = "Forward";
        link.BackwardLabel = "Backward";

        // Assert
        Assert.Equal("Forward", link.ForwardLabel);
        Assert.Equal("Backward", link.BackwardLabel);
    }

    [Theory]
    [InlineData(DependencyType.Required)]
    [InlineData(DependencyType.Optional)]
    [InlineData(DependencyType.Weak)]
    [InlineData(DependencyType.Strong)]
    public void DependencyType_AllValuesAreValid(DependencyType dependencyType)
    {
        // Arrange
        var link = new DependencyLinkModel(_sourceNode, _targetNode);

        // Act
        link.Type = dependencyType;

        // Assert
        Assert.Equal(dependencyType, link.Type);
    }

    [Theory]
    [InlineData(LinkMarker.None)]
    [InlineData(LinkMarker.Arrow)]
    [InlineData(LinkMarker.Circle)]
    [InlineData(LinkMarker.Diamond)]
    [InlineData(LinkMarker.Square)]
    public void LinkMarker_AllValuesAreValid(LinkMarker marker)
    {
        // Arrange
        var link = new CustomLinkModel(_sourceNode, _targetNode);

        // Act
        link.TargetMarker = marker;
        link.SourceMarker = marker;

        // Assert
        Assert.Equal(marker, link.TargetMarker);
        Assert.Equal(marker, link.SourceMarker);
    }
}
