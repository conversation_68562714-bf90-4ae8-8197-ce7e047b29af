using Microsoft.EntityFrameworkCore;
using DiagramEditor.Models;

namespace DiagramEditor.Data
{
    public class DiagramDbContext : DbContext
    {
        public DiagramDbContext(DbContextOptions<DiagramDbContext> options)
            : base(options)
        {
        }

        public DbSet<DiagramEntity> Diagrams { get; set; }
        public DbSet<NodeEntity> Nodes { get; set; }
        public DbSet<PortEntity> Ports { get; set; }
        public DbSet<LinkEntity> Links { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configuração para DiagramEntity
            modelBuilder.Entity<DiagramEntity>()
                .HasMany(d => d.Nodes)
                .WithOne(n => n.Diagram)
                .HasForeignKey(n => n.DiagramId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DiagramEntity>()
                .HasMany(d => d.Links)
                .WithOne(l => l.Diagram)
                .HasForeignKey(l => l.DiagramId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configuração para NodeEntity
            modelBuilder.Entity<NodeEntity>()
                .HasMany(n => n.Ports)
                .WithOne(p => p.Node)
                .HasForeignKey(p => p.NodeId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configuração para LinkEntity
            modelBuilder.Entity<LinkEntity>()
                .Property(l => l.Width)
                .HasDefaultValue(2);
        }
    }
}