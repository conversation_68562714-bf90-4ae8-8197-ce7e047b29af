{"version": 2, "dgSpecHash": "zXk5FgDdPSY=", "success": true, "projectFilePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\nanoid\\1.0.0\\nanoid.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\svgpathproperties\\1.0.0\\svgpathproperties.1.0.0.nupkg.sha512"], "logs": [{"code": "NU1602", "level": "Warning", "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency Nanoid. Nanoid 1.0.0 was resolved instead.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "libraryId": "Nanoid", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}, {"code": "NU1602", "level": "Warning", "message": "Z.Blazor.Diagrams.Core does not provide an inclusive lower bound for dependency SvgPathProperties. SvgPathProperties 1.0.0 was resolved instead.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Algorithms\\Blazor.Diagrams.Algorithms.csproj", "libraryId": "SvgPathProperties", "targetGraphs": ["net6.0", "net7.0", "net8.0", "net9.0"]}]}