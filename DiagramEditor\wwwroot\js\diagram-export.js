// Funções para exportação de diagramas

window.exportDiagramAsPNG = function(canvasId) {
    try {
        const canvas = document.querySelector('.diagram-canvas svg') || document.querySelector('svg');
        if (!canvas) {
            console.error('Canvas não encontrado');
            return;
        }

        // Criar um canvas temporário
        const tempCanvas = document.createElement('canvas');
        const ctx = tempCanvas.getContext('2d');
        
        // Obter dimensões do SVG
        const svgRect = canvas.getBoundingClientRect();
        tempCanvas.width = svgRect.width;
        tempCanvas.height = svgRect.height;

        // Converter SVG para imagem
        const svgData = new XMLSerializer().serializeToString(canvas);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = function() {
            ctx.drawImage(img, 0, 0);
            
            // Converter para PNG e fazer download
            tempCanvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `diagrama_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
            
            URL.revokeObjectURL(svgUrl);
        };
        img.src = svgUrl;
    } catch (error) {
        console.error('Erro ao exportar PNG:', error);
    }
};

window.exportDiagramAsPDF = function(canvasId) {
    try {
        // Usar html2canvas para capturar o diagrama
        const diagramContainer = document.querySelector('.diagram-canvas') || document.querySelector('[data-diagram]');
        if (!diagramContainer) {
            console.error('Container do diagrama não encontrado');
            return;
        }

        // Importar jsPDF dinamicamente se disponível
        if (typeof window.jsPDF !== 'undefined') {
            html2canvas(diagramContainer, {
                backgroundColor: '#ffffff',
                scale: 2,
                useCORS: true
            }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new window.jsPDF();
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;

                let position = 0;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save(`diagrama_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.pdf`);
            });
        } else {
            // Fallback: exportar como PNG se jsPDF não estiver disponível
            console.warn('jsPDF não disponível, exportando como PNG');
            exportDiagramAsPNG(canvasId);
        }
    } catch (error) {
        console.error('Erro ao exportar PDF:', error);
    }
};

window.downloadFile = function(filename, content, contentType) {
    try {
        const blob = new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Erro ao fazer download do arquivo:', error);
    }
};

// Função para capturar screenshot do diagrama
window.captureScreenshot = function() {
    try {
        const canvas = document.querySelector('.diagram-canvas svg') || document.querySelector('svg');
        if (!canvas) {
            console.error('Canvas não encontrado');
            return null;
        }

        const svgData = new XMLSerializer().serializeToString(canvas);
        return svgData;
    } catch (error) {
        console.error('Erro ao capturar screenshot:', error);
        return null;
    }
};

// Função para aplicar zoom programaticamente
window.setDiagramZoom = function(zoomLevel) {
    try {
        const diagramContainer = document.querySelector('.diagram-canvas');
        if (diagramContainer) {
            diagramContainer.style.transform = `scale(${zoomLevel})`;
            diagramContainer.style.transformOrigin = 'center center';
        }
    } catch (error) {
        console.error('Erro ao aplicar zoom:', error);
    }
};

// Função para centralizar o diagrama
window.centerDiagram = function() {
    try {
        const diagramContainer = document.querySelector('.diagram-canvas');
        if (diagramContainer) {
            diagramContainer.style.transform = 'scale(1) translate(0, 0)';
            diagramContainer.style.transformOrigin = 'center center';
        }
    } catch (error) {
        console.error('Erro ao centralizar diagrama:', error);
    }
};

// Inicialização quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    console.log('Diagram export functions loaded');
    
    // Adicionar estilos CSS para melhor aparência dos diagramas
    const style = document.createElement('style');
    style.textContent = `
        .diagram-canvas {
            transition: transform 0.3s ease;
        }
        
        .diagram-node {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .diagram-node:hover {
            filter: brightness(1.1);
        }
        
        .diagram-node.selected {
            filter: drop-shadow(0 0 8px rgba(255, 87, 34, 0.6));
        }
    `;
    document.head.appendChild(style);
});
