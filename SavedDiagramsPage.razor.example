@page "/saved-diagrams"
@using Blazor.Diagrams.Core.Persistence
@using DiagramEditor.Models
@inject IDiagramPersistenceService DiagramPersistenceService
@inject NavigationManager NavigationManager
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
    <MudText Typo="Typo.h4" Class="mb-4">Diagramas Salvos</MudText>

    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="() => NavigationManager.NavigateTo('/diagram-editor')" StartIcon="@Icons.Material.Filled.Add" Class="mb-4">
        Novo Diagrama
    </MudButton>

    @if (_loading)
    {
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
    }
    else if (_diagrams == null || !_diagrams.Any())
    {
        <MudAlert Severity="Severity.Info" Class="my-4">Nenhum diagrama salvo encontrado. Crie um novo diagrama para começar.</MudAlert>
    }
    else
    {
        <MudTable Items="_diagrams" Hover="true" Breakpoint="Breakpoint.Sm" Loading="_loading" LoadingProgressColor="Color.Info">
            <HeaderContent>
                <MudTh>Nome</MudTh>
                <MudTh>Descrição</MudTh>
                <MudTh>Data de Criação</MudTh>
                <MudTh>Última Atualização</MudTh>
                <MudTh>Ações</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="Nome">@context.Name</MudTd>
                <MudTd DataLabel="Descrição">@context.Description</MudTd>
                <MudTd DataLabel="Data de Criação">@context.CreatedAt.ToString("dd/MM/yyyy HH:mm")</MudTd>
                <MudTd DataLabel="Última Atualização">@context.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</MudTd>
                <MudTd>
                    <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" OnClick="() => OpenDiagram(context.Id)" />
                    <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="() => ConfirmDelete(context)" />
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[] { 5, 10, 25, 50 }" />
            </PagerContent>
        </MudTable>
    }
</MudContainer>

@code {
    private List<DiagramEntity> _diagrams;
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDiagrams();
    }

    private async Task LoadDiagrams()
    {
        _loading = true;
        try
        {
            _diagrams = await DiagramPersistenceService.GetAllDiagramsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar diagramas: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private void OpenDiagram(string id)
    {
        NavigationManager.NavigateTo($"/diagram-editor/{id}");
    }

    private async Task ConfirmDelete(DiagramEntity diagram)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", $"Tem certeza que deseja excluir o diagrama '{diagram.Name}'? Esta ação não pode ser desfeita." },
            { "ButtonText", "Excluir" },
            { "Color", Color.Error }
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>("Confirmar Exclusão", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await DeleteDiagram(diagram.Id);
        }
    }

    private async Task DeleteDiagram(string id)
    {
        _loading = true;
        try
        {
            var success = await DiagramPersistenceService.DeleteDiagramAsync(id);
            if (success)
            {
                Snackbar.Add("Diagrama excluído com sucesso", Severity.Success);
                await LoadDiagrams();
            }
            else
            {
                Snackbar.Add("Não foi possível excluir o diagrama", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao excluir diagrama: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }
}