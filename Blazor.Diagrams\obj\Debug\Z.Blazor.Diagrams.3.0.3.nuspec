﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Z.Blazor.Diagrams</id>
    <version>3.0.3</version>
    <authors>zHaytam</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>ZBD.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://blazor-diagrams.zhaytam.com/</projectUrl>
    <description>A fully customizable and extensible all-purpose diagrams library for Blazor</description>
    <tags>blazor diagrams diagramming svg drag</tags>
    <repository type="git" url="https://github.com/Blazor-Diagrams/Blazor.Diagrams" commit="492bec3b754def37506c1b93b466ab8b8c8b81ba" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components" version="6.0.25" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components.Web" version="6.0.25" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components" version="7.0.14" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components.Web" version="7.0.14" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components" version="[8.0.0, 9.0.0)" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components.Web" version="[8.0.0, 9.0.0)" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net9.0">
        <dependency id="Z.Blazor.Diagrams.Core" version="3.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components" version="[9.0.0, 10.0.0)" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Components.Web" version="[9.0.0, 10.0.0)" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\bin\Debug\net6.0\Blazor.Diagrams.dll" target="lib\net6.0\Blazor.Diagrams.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\bin\Debug\net7.0\Blazor.Diagrams.dll" target="lib\net7.0\Blazor.Diagrams.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\bin\Debug\net8.0\Blazor.Diagrams.dll" target="lib\net8.0\Blazor.Diagrams.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\bin\Debug\net9.0\Blazor.Diagrams.dll" target="lib\net9.0\Blazor.Diagrams.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\scopedcss\projectbundle\Z.Blazor.Diagrams.bundle.scp.css" target="staticwebassets\Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\default.styles.css" target="staticwebassets\default.styles.css" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\default.styles.min.css" target="staticwebassets\default.styles.min.css" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\script.js" target="staticwebassets\script.js" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\script.min.js" target="staticwebassets\script.min.js" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\style.css" target="staticwebassets\style.css" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\wwwroot\style.min.css" target="staticwebassets\style.min.css" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\staticwebassets\msbuild.Z.Blazor.Diagrams.Microsoft.AspNetCore.StaticWebAssetEndpoints.props" target="build\Microsoft.AspNetCore.StaticWebAssetEndpoints.props" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\staticwebassets\msbuild.Z.Blazor.Diagrams.Microsoft.AspNetCore.StaticWebAssets.props" target="build\Microsoft.AspNetCore.StaticWebAssets.props" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\staticwebassets\msbuild.build.Z.Blazor.Diagrams.props" target="build\Z.Blazor.Diagrams.props" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Z.Blazor.Diagrams.props" target="buildMultiTargeting\Z.Blazor.Diagrams.props" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Z.Blazor.Diagrams.props" target="buildTransitive\Z.Blazor.Diagrams.props" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\ZBD.png" target="\ZBD.png" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\README.md" target="\README.md" />
  </files>
</package>