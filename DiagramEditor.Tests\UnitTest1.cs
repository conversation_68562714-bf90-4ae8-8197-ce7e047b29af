﻿using DiagramEditor.Services;
using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Options;

namespace DiagramEditor.Tests;

public class DiagramServiceTests
{
    private readonly DiagramService _diagramService;
    private readonly BlazorDiagram _diagram;

    public DiagramServiceTests()
    {
        _diagramService = new DiagramService();
        _diagram = new BlazorDiagram(new BlazorDiagramOptions());
    }

    [Fact]
    public void SerializeDiagram_WithEmptyDiagram_ReturnsValidJson()
    {
        // Act
        var result = _diagramService.SerializeDiagram(_diagram);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("Nodes", result);
        Assert.Contains("Links", result);
    }

    [Fact]
    public void SerializeDiagram_WithSingleNode_ReturnsCorrectJson()
    {
        // Arrange
        var node = new NodeModel(new Point(100, 50))
        {
            Title = "Test Node"
        };
        _diagram.Nodes.Add(node);

        // Act
        var result = _diagramService.SerializeDiagram(_diagram);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("Test Node", result);
        Assert.Contains("100", result);
        Assert.Contains("50", result);
    }

    [Fact]
    public void DeserializeDiagram_WithValidJson_RestoresNodes()
    {
        // Arrange
        var originalNode = new NodeModel(new Point(200, 100))
        {
            Title = "Original Node"
        };
        _diagram.Nodes.Add(originalNode);
        var serializedData = _diagramService.SerializeDiagram(_diagram);

        var newDiagram = new BlazorDiagram(new BlazorDiagramOptions());

        // Act
        _diagramService.DeserializeDiagram(newDiagram, serializedData);

        // Assert
        Assert.Single(newDiagram.Nodes);
        Assert.Equal("Original Node", newDiagram.Nodes.First().Title);
    }

    [Fact]
    public void DeserializeDiagram_WithInvalidJson_ThrowsException()
    {
        // Arrange
        var invalidJson = "{ invalid json }";

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() =>
            _diagramService.DeserializeDiagram(_diagram, invalidJson));
    }
}
