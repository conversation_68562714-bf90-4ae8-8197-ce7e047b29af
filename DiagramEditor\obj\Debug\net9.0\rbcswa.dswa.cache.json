{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8NYkRQXleQFBM3EOomMcTDqpGSdHgeRgMXG3JyX6mqo=", "HleW9kW19wtqPZUYDHFi7BIkMi5vcHNXaf+GfFAJX98=", "DmDpdO1hzsd69fOObH6VBhifKF49CgQPAztdSEPLurY=", "T73KOe+T7Ekk9ua3pOGV4JW1QiWqR3wFpY1upmhkOjM=", "q16dbgV+RWst70yg5iX6zFbgvQPB5GP7/Zq/MCciou4=", "XJ3PtELHQux9Mzene+OMnzIeNp+q3jdZdV1vYQ+T4sg=", "ccFsQCxU+exxnOh8VgP4ms/UWjiWmWKczroanfI1OEM=", "h68xlE9g3PjZbzhCfL+YpzcORvDih2jw1LzUg0M4g8U=", "rFxcg4QKQrYnooCMcVjStnPxFrdZibiuykzCdL6h4bI=", "BM3qS2J6WGl1NpkjOGjEVe0yOyEhUDReGZML8JNqlec=", "FQG4eL4KTPmJuqwy3WhdLLNxZQK2vVhVtVyKw0M97cE=", "k/BHuMKEBIYAv8eNHkt3pGUftrKmXPnMx4SPt5yGXO8=", "2H3oNqohFidtRpmfBM5ICIsmP6I1Vp07I3vAJZrJf4M=", "NGQTrPqrijR/7GboW7AY8CGKZ60iasNtjD4Ts/nLqD8=", "vXAkXRfaVzxPCusJHR4MpN+YmNfJiV2we9ciMUxBwa8="], "CachedAssets": {"vXAkXRfaVzxPCusJHR4MpN+YmNfJiV2we9ciMUxBwa8=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\1jbi96n95o-1b9liozj85.gz", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint=1b9liozj85}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eopxey7q6y", "Integrity": "wnm9C9FeRWGhqcYjhR33VW2xTlVRpOOYJCTwBYUx3tU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\DiagramEditor.bundle.scp.css", "FileLength": 943, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "NGQTrPqrijR/7GboW7AY8CGKZ60iasNtjD4Ts/nLqD8=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\w2u8ujiltp-v3ikh3owht.gz", "SourceId": "DiagramEditor", "SourceType": "Computed", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "DiagramEditor#[.{fingerprint=v3ikh3owht}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l13wvbmc9f", "Integrity": "lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\scopedcss\\bundle\\DiagramEditor.styles.css", "FileLength": 979, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "2H3oNqohFidtRpmfBM5ICIsmP6I1Vp07I3vAJZrJf4M=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\nnuo9ll29b-wkr7rr9ybj.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/responsive#[.{fingerprint=wkr7rr9ybj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3idrsuck7n", "Integrity": "5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\responsive.js", "FileLength": 2913, "LastWriteTime": "2025-07-10T01:50:00.3926362+00:00"}, "k/BHuMKEBIYAv8eNHkt3pGUftrKmXPnMx4SPt5yGXO8=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\hxj55fipr6-hz8bn5rrj8.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/diagram-export#[.{fingerprint=hz8bn5rrj8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "seii0zkrfo", "Integrity": "tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\diagram-export.js", "FileLength": 1716, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "FQG4eL4KTPmJuqwy3WhdLLNxZQK2vVhVtVyKw0M97cE=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\n6oqtztnvz-u872bpsf3j.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "js/blazor-diagrams.min#[.{fingerprint=u872bpsf3j}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfigjyy21o", "Integrity": "pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\js\\blazor-diagrams.min.js", "FileLength": 520, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "BM3qS2J6WGl1NpkjOGjEVe0yOyEhUDReGZML8JNqlec=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\d2wc3cjy5s-zh9s866nx2.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/site#[.{fingerprint=zh9s866nx2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1cpc6dqu4", "Integrity": "L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\site.css", "FileLength": 1656, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "rFxcg4QKQrYnooCMcVjStnPxFrdZibiuykzCdL6h4bI=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\issenhua16-5bzwdl5l6x.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/README#[.{fingerprint=5bzwdl5l6x}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jbzbw5vlik", "Integrity": "cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\README.md", "FileLength": 1484, "LastWriteTime": "2025-07-10T01:46:43.0734162+00:00"}, "h68xlE9g3PjZbzhCfL+YpzcORvDih2jw1LzUg0M4g8U=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\v5fjcxz96u-sjnzgf7e1h.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint=sjnzgf7e1h}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t673mz4ep7", "Integrity": "27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 13464, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "ccFsQCxU+exxnOh8VgP4ms/UWjiWmWKczroanfI1OEM=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\t9ejy9ytcs-wk8x8xm0ah.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint=wk8x8xm0ah}]?.otf.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gzw6z059fx", "Integrity": "hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 13066, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "XJ3PtELHQux9Mzene+OMnzIeNp+q3jdZdV1vYQ+T4sg=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\19c9g5r2i8-cmapd0fi15.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint=cmapd0fi15}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qvz7hczgpc", "Integrity": "ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 2106, "LastWriteTime": "2025-07-10T01:46:43.072774+00:00"}, "HleW9kW19wtqPZUYDHFi7BIkMi5vcHNXaf+GfFAJX98=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-rftke82za3.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dplvskzkxa", "Integrity": "X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "FileLength": 9705, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "8NYkRQXleQFBM3EOomMcTDqpGSdHgeRgMXG3JyX6mqo=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-1xiwpnv0y9.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tvdvut284s", "Integrity": "9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "FileLength": 55077, "LastWriteTime": "2025-07-10T01:46:43.0782726+00:00"}, "q16dbgV+RWst70yg5iX6zFbgvQPB5GP7/Zq/MCciou4=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\ufw63sd64p-8inm30yfxf.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m532s4naxw", "Integrity": "epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 74642, "LastWriteTime": "2025-07-10T01:46:43.0782726+00:00"}, "T73KOe+T7Ekk9ua3pOGV4JW1QiWqR3wFpY1upmhkOjM=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\7om3v203a8-bpk8xqwxhs.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint=bpk8xqwxhs}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "li2ohn2azb", "Integrity": "vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 23851, "LastWriteTime": "2025-07-10T01:46:43.0739198+00:00"}, "DmDpdO1hzsd69fOObH6VBhifKF49CgQPAztdSEPLurY=": {"Identity": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\pti3sctzrl-kjpcwcpl0m.gz", "SourceId": "DiagramEditor", "SourceType": "Discovered", "ContentRoot": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/DiagramEditor", "RelativePath": "css/blazor-diagrams.min#[.{fingerprint=kjpcwcpl0m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "Server", "RelatedAsset": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79vxboh8lv", "Integrity": "hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\css\\blazor-diagrams.min.css", "FileLength": 418, "LastWriteTime": "2025-07-10T01:46:43.072774+00:00"}}, "CachedCopyCandidates": {}}