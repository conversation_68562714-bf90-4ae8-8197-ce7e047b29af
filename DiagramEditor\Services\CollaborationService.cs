using Microsoft.AspNetCore.SignalR.Client;
using DiagramEditor.Hubs;

namespace DiagramEditor.Services;

/// <summary>
/// Serviço para colaboração em tempo real
/// </summary>
public interface ICollaborationService
{
    bool IsConnected { get; }
    string? CurrentDiagramId { get; }
    List<UserInfo> ConnectedUsers { get; }
    
    event Action<UserInfo>? UserJoined;
    event Action<UserInfo>? UserLeft;
    event Action<List<UserInfo>>? UsersUpdated;
    event Action<DiagramChangeEvent>? DiagramChangeReceived;
    event Action<CursorPosition>? CursorPositionReceived;
    event Action<SelectionEvent>? SelectionReceived;
    event Action<ElementLock>? ElementLocked;
    event Action<string, ElementLock>? ElementUnlocked;
    event Action<string>? DiagramStateReceived;

    Task StartAsync();
    Task StopAsync();
    Task JoinDiagramAsync(string diagramId, string userName, string userColor);
    Task LeaveDiagramAsync();
    Task SendDiagramChangeAsync(DiagramChangeEvent changeEvent);
    Task SendCursorPositionAsync(double x, double y);
    Task SendSelectionAsync(List<string> selectedElementIds);
    Task RequestElementLockAsync(string elementId);
    Task ReleaseElementLockAsync(string elementId);
}

public class CollaborationService : ICollaborationService, IAsyncDisposable
{
    private HubConnection? _hubConnection;
    private readonly List<UserInfo> _connectedUsers = new();
    private string? _currentDiagramId;
    private string _currentUserName = "";
    private string _currentUserColor = "#2196F3";

    public bool IsConnected => _hubConnection?.State == HubConnectionState.Connected;
    public string? CurrentDiagramId => _currentDiagramId;
    public List<UserInfo> ConnectedUsers => _connectedUsers.ToList();

    public event Action<UserInfo>? UserJoined;
    public event Action<UserInfo>? UserLeft;
    public event Action<List<UserInfo>>? UsersUpdated;
    public event Action<DiagramChangeEvent>? DiagramChangeReceived;
    public event Action<CursorPosition>? CursorPositionReceived;
    public event Action<SelectionEvent>? SelectionReceived;
    public event Action<ElementLock>? ElementLocked;
    public event Action<string, ElementLock>? ElementUnlocked;
    public event Action<string>? DiagramStateReceived;

    public async Task StartAsync()
    {
        if (_hubConnection != null)
            return;

        _hubConnection = new HubConnectionBuilder()
            .WithUrl("/diagramhub")
            .WithAutomaticReconnect()
            .Build();

        // Configurar handlers de eventos
        SetupEventHandlers();

        await _hubConnection.StartAsync();
    }

    public async Task StopAsync()
    {
        if (_hubConnection != null)
        {
            await _hubConnection.StopAsync();
            await _hubConnection.DisposeAsync();
            _hubConnection = null;
        }
    }

    public async Task JoinDiagramAsync(string diagramId, string userName, string userColor)
    {
        if (_hubConnection?.State != HubConnectionState.Connected)
            throw new InvalidOperationException("Não conectado ao hub");

        _currentDiagramId = diagramId;
        _currentUserName = userName;
        _currentUserColor = userColor;

        await _hubConnection.InvokeAsync("JoinDiagram", diagramId, userName, userColor);
    }

    public async Task LeaveDiagramAsync()
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        await _hubConnection.InvokeAsync("LeaveDiagram", _currentDiagramId);
        
        _currentDiagramId = null;
        _connectedUsers.Clear();
        UsersUpdated?.Invoke(_connectedUsers);
    }

    public async Task SendDiagramChangeAsync(DiagramChangeEvent changeEvent)
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        await _hubConnection.InvokeAsync("SendDiagramChange", _currentDiagramId, changeEvent);
    }

    public async Task SendCursorPositionAsync(double x, double y)
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        var position = new CursorPosition
        {
            X = x,
            Y = y
        };

        await _hubConnection.InvokeAsync("SendCursorPosition", _currentDiagramId, position);
    }

    public async Task SendSelectionAsync(List<string> selectedElementIds)
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        var selection = new SelectionEvent
        {
            SelectedElementIds = selectedElementIds
        };

        await _hubConnection.InvokeAsync("SendSelection", _currentDiagramId, selection);
    }

    public async Task RequestElementLockAsync(string elementId)
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        await _hubConnection.InvokeAsync("RequestDiagramLock", _currentDiagramId, elementId);
    }

    public async Task ReleaseElementLockAsync(string elementId)
    {
        if (_hubConnection?.State != HubConnectionState.Connected || _currentDiagramId == null)
            return;

        await _hubConnection.InvokeAsync("ReleaseDiagramLock", _currentDiagramId, elementId);
    }

    private void SetupEventHandlers()
    {
        if (_hubConnection == null) return;

        _hubConnection.On<UserInfo>("UserJoined", (userInfo) =>
        {
            _connectedUsers.Add(userInfo);
            UserJoined?.Invoke(userInfo);
            UsersUpdated?.Invoke(_connectedUsers);
        });

        _hubConnection.On<UserInfo>("UserLeft", (userInfo) =>
        {
            _connectedUsers.RemoveAll(u => u.ConnectionId == userInfo.ConnectionId);
            UserLeft?.Invoke(userInfo);
            UsersUpdated?.Invoke(_connectedUsers);
        });

        _hubConnection.On<IEnumerable<UserInfo>>("UsersConnected", (users) =>
        {
            _connectedUsers.Clear();
            _connectedUsers.AddRange(users);
            UsersUpdated?.Invoke(_connectedUsers);
        });

        _hubConnection.On<DiagramChangeEvent>("DiagramChangeReceived", (changeEvent) =>
        {
            DiagramChangeReceived?.Invoke(changeEvent);
        });

        _hubConnection.On<CursorPosition>("CursorPositionReceived", (position) =>
        {
            CursorPositionReceived?.Invoke(position);
        });

        _hubConnection.On<SelectionEvent>("SelectionReceived", (selection) =>
        {
            SelectionReceived?.Invoke(selection);
        });

        _hubConnection.On<ElementLock>("ElementLocked", (lockInfo) =>
        {
            ElementLocked?.Invoke(lockInfo);
        });

        _hubConnection.On<string, ElementLock>("ElementUnlocked", (elementId, lockInfo) =>
        {
            ElementUnlocked?.Invoke(elementId, lockInfo);
        });

        _hubConnection.On<string>("DiagramStateReceived", (state) =>
        {
            DiagramStateReceived?.Invoke(state);
        });

        _hubConnection.Reconnecting += (error) =>
        {
            // Log reconnection attempt
            return Task.CompletedTask;
        };

        _hubConnection.Reconnected += (connectionId) =>
        {
            // Rejoin diagram if we were in one
            if (!string.IsNullOrEmpty(_currentDiagramId))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await JoinDiagramAsync(_currentDiagramId, _currentUserName, _currentUserColor);
                    }
                    catch
                    {
                        // Handle rejoin failure
                    }
                });
            }
            return Task.CompletedTask;
        };

        _hubConnection.Closed += (error) =>
        {
            _connectedUsers.Clear();
            UsersUpdated?.Invoke(_connectedUsers);
            return Task.CompletedTask;
        };
    }

    public async ValueTask DisposeAsync()
    {
        if (_hubConnection != null)
        {
            await _hubConnection.DisposeAsync();
        }
    }
}

/// <summary>
/// Factory para criar eventos de mudança no diagrama
/// </summary>
public static class DiagramChangeEventFactory
{
    public static DiagramChangeEvent CreateNodeAdded(string nodeId, string nodeData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.NodeAdded,
            ElementId = nodeId,
            NewState = nodeData
        };
    }

    public static DiagramChangeEvent CreateNodeUpdated(string nodeId, string oldData, string newData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.NodeUpdated,
            ElementId = nodeId,
            OldState = oldData,
            NewState = newData
        };
    }

    public static DiagramChangeEvent CreateNodeDeleted(string nodeId, string nodeData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.NodeDeleted,
            ElementId = nodeId,
            OldState = nodeData
        };
    }

    public static DiagramChangeEvent CreateLinkAdded(string linkId, string linkData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.LinkAdded,
            ElementId = linkId,
            NewState = linkData
        };
    }

    public static DiagramChangeEvent CreateLinkUpdated(string linkId, string oldData, string newData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.LinkUpdated,
            ElementId = linkId,
            OldState = oldData,
            NewState = newData
        };
    }

    public static DiagramChangeEvent CreateLinkDeleted(string linkId, string linkData)
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.LinkDeleted,
            ElementId = linkId,
            OldState = linkData
        };
    }

    public static DiagramChangeEvent CreateDiagramCleared()
    {
        return new DiagramChangeEvent
        {
            Type = ChangeType.DiagramCleared,
            ElementId = "diagram"
        };
    }
}
