using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;

namespace DiagramEditor.Hubs;

/// <summary>
/// Hub do SignalR para colaboração em tempo real em diagramas
/// </summary>
public class DiagramCollaborationHub : Hub
{
    private static readonly ConcurrentDictionary<string, DiagramSession> _sessions = new();
    private static readonly ConcurrentDictionary<string, UserInfo> _connectedUsers = new();

    public async Task JoinDiagram(string diagramId, string userName, string userColor)
    {
        var connectionId = Context.ConnectionId;
        
        // Adicionar usuário à sessão
        var userInfo = new UserInfo
        {
            ConnectionId = connectionId,
            UserName = userName,
            Color = userColor,
            DiagramId = diagramId,
            JoinedAt = DateTime.UtcNow
        };

        _connectedUsers[connectionId] = userInfo;

        // Criar ou obter sessão do diagrama
        var session = _sessions.GetOrAdd(diagramId, _ => new DiagramSession { DiagramId = diagramId });
        session.Users[connectionId] = userInfo;

        // Adicionar à sala do diagrama
        await Groups.AddToGroupAsync(connectionId, $"diagram_{diagramId}");

        // Notificar outros usuários sobre a entrada
        await Clients.Group($"diagram_{diagramId}")
            .SendAsync("UserJoined", userInfo);

        // Enviar lista de usuários conectados para o novo usuário
        await Clients.Caller.SendAsync("UsersConnected", session.Users.Values);

        // Enviar estado atual do diagrama se existir
        if (!string.IsNullOrEmpty(session.CurrentState))
        {
            await Clients.Caller.SendAsync("DiagramStateReceived", session.CurrentState);
        }
    }

    public async Task LeaveDiagram(string diagramId)
    {
        var connectionId = Context.ConnectionId;
        
        if (_connectedUsers.TryRemove(connectionId, out var userInfo))
        {
            // Remover da sessão
            if (_sessions.TryGetValue(diagramId, out var session))
            {
                session.Users.TryRemove(connectionId, out _);
                
                // Se não há mais usuários, remover a sessão
                if (session.Users.IsEmpty)
                {
                    _sessions.TryRemove(diagramId, out _);
                }
            }

            // Remover da sala
            await Groups.RemoveFromGroupAsync(connectionId, $"diagram_{diagramId}");

            // Notificar outros usuários sobre a saída
            await Clients.Group($"diagram_{diagramId}")
                .SendAsync("UserLeft", userInfo);
        }
    }

    public async Task SendDiagramChange(string diagramId, DiagramChangeEvent changeEvent)
    {
        var connectionId = Context.ConnectionId;
        
        if (!_connectedUsers.TryGetValue(connectionId, out var userInfo))
            return;

        // Adicionar informações do usuário ao evento
        changeEvent.UserId = connectionId;
        changeEvent.UserName = userInfo.UserName;
        changeEvent.Timestamp = DateTime.UtcNow;

        // Atualizar estado da sessão
        if (_sessions.TryGetValue(diagramId, out var session))
        {
            session.LastModified = DateTime.UtcNow;
            session.LastModifiedBy = userInfo.UserName;
            
            // Aplicar mudança ao estado (simplificado)
            ApplyChangeToSession(session, changeEvent);
        }

        // Enviar mudança para outros usuários (exceto o remetente)
        await Clients.GroupExcept($"diagram_{diagramId}", connectionId)
            .SendAsync("DiagramChangeReceived", changeEvent);
    }

    public async Task SendCursorPosition(string diagramId, CursorPosition position)
    {
        var connectionId = Context.ConnectionId;
        
        if (!_connectedUsers.TryGetValue(connectionId, out var userInfo))
            return;

        position.UserId = connectionId;
        position.UserName = userInfo.UserName;
        position.Color = userInfo.Color;

        // Enviar posição do cursor para outros usuários
        await Clients.GroupExcept($"diagram_{diagramId}", connectionId)
            .SendAsync("CursorPositionReceived", position);
    }

    public async Task SendSelection(string diagramId, SelectionEvent selection)
    {
        var connectionId = Context.ConnectionId;
        
        if (!_connectedUsers.TryGetValue(connectionId, out var userInfo))
            return;

        selection.UserId = connectionId;
        selection.UserName = userInfo.UserName;
        selection.Color = userInfo.Color;

        // Enviar seleção para outros usuários
        await Clients.GroupExcept($"diagram_{diagramId}", connectionId)
            .SendAsync("SelectionReceived", selection);
    }

    public async Task RequestDiagramLock(string diagramId, string elementId)
    {
        var connectionId = Context.ConnectionId;
        
        if (!_connectedUsers.TryGetValue(connectionId, out var userInfo))
            return;

        if (_sessions.TryGetValue(diagramId, out var session))
        {
            var lockKey = $"{diagramId}_{elementId}";
            
            // Verificar se o elemento já está bloqueado
            if (session.LockedElements.TryGetValue(elementId, out var existingLock))
            {
                if (existingLock.UserId != connectionId)
                {
                    // Elemento já bloqueado por outro usuário
                    await Clients.Caller.SendAsync("LockDenied", elementId, existingLock);
                    return;
                }
            }

            // Conceder bloqueio
            var lockInfo = new ElementLock
            {
                ElementId = elementId,
                UserId = connectionId,
                UserName = userInfo.UserName,
                LockedAt = DateTime.UtcNow
            };

            session.LockedElements[elementId] = lockInfo;

            // Notificar todos os usuários sobre o bloqueio
            await Clients.Group($"diagram_{diagramId}")
                .SendAsync("ElementLocked", lockInfo);
        }
    }

    public async Task ReleaseDiagramLock(string diagramId, string elementId)
    {
        var connectionId = Context.ConnectionId;
        
        if (_sessions.TryGetValue(diagramId, out var session))
        {
            if (session.LockedElements.TryRemove(elementId, out var lockInfo))
            {
                // Notificar todos os usuários sobre a liberação
                await Clients.Group($"diagram_{diagramId}")
                    .SendAsync("ElementUnlocked", elementId, lockInfo);
            }
        }
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var connectionId = Context.ConnectionId;
        
        if (_connectedUsers.TryRemove(connectionId, out var userInfo))
        {
            // Remover de todas as sessões e liberar bloqueios
            foreach (var session in _sessions.Values)
            {
                session.Users.TryRemove(connectionId, out _);
                
                // Liberar todos os bloqueios do usuário
                var userLocks = session.LockedElements
                    .Where(kvp => kvp.Value.UserId == connectionId)
                    .ToList();

                foreach (var lockPair in userLocks)
                {
                    session.LockedElements.TryRemove(lockPair.Key, out _);
                    
                    await Clients.Group($"diagram_{session.DiagramId}")
                        .SendAsync("ElementUnlocked", lockPair.Key, lockPair.Value);
                }

                // Notificar sobre a saída do usuário
                await Clients.Group($"diagram_{session.DiagramId}")
                    .SendAsync("UserLeft", userInfo);
            }
        }

        await base.OnDisconnectedAsync(exception);
    }

    private void ApplyChangeToSession(DiagramSession session, DiagramChangeEvent changeEvent)
    {
        // Implementação simplificada - em produção, usar operational transforms
        switch (changeEvent.Type)
        {
            case ChangeType.NodeAdded:
            case ChangeType.NodeUpdated:
            case ChangeType.NodeDeleted:
            case ChangeType.LinkAdded:
            case ChangeType.LinkUpdated:
            case ChangeType.LinkDeleted:
                // Atualizar estado da sessão
                session.CurrentState = changeEvent.NewState ?? session.CurrentState;
                break;
        }
    }
}

/// <summary>
/// Informações do usuário conectado
/// </summary>
public class UserInfo
{
    public string ConnectionId { get; set; } = "";
    public string UserName { get; set; } = "";
    public string Color { get; set; } = "#2196F3";
    public string DiagramId { get; set; } = "";
    public DateTime JoinedAt { get; set; }
}

/// <summary>
/// Sessão de colaboração de um diagrama
/// </summary>
public class DiagramSession
{
    public string DiagramId { get; set; } = "";
    public ConcurrentDictionary<string, UserInfo> Users { get; set; } = new();
    public ConcurrentDictionary<string, ElementLock> LockedElements { get; set; } = new();
    public string CurrentState { get; set; } = "";
    public DateTime LastModified { get; set; }
    public string LastModifiedBy { get; set; } = "";
}

/// <summary>
/// Evento de mudança no diagrama
/// </summary>
public class DiagramChangeEvent
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public ChangeType Type { get; set; }
    public string ElementId { get; set; } = "";
    public string? OldState { get; set; }
    public string? NewState { get; set; }
    public string UserId { get; set; } = "";
    public string UserName { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Posição do cursor do usuário
/// </summary>
public class CursorPosition
{
    public string UserId { get; set; } = "";
    public string UserName { get; set; } = "";
    public string Color { get; set; } = "";
    public double X { get; set; }
    public double Y { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Evento de seleção
/// </summary>
public class SelectionEvent
{
    public string UserId { get; set; } = "";
    public string UserName { get; set; } = "";
    public string Color { get; set; } = "";
    public List<string> SelectedElementIds { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Informações de bloqueio de elemento
/// </summary>
public class ElementLock
{
    public string ElementId { get; set; } = "";
    public string UserId { get; set; } = "";
    public string UserName { get; set; } = "";
    public DateTime LockedAt { get; set; }
}

/// <summary>
/// Tipos de mudanças no diagrama
/// </summary>
public enum ChangeType
{
    NodeAdded,
    NodeUpdated,
    NodeDeleted,
    LinkAdded,
    LinkUpdated,
    LinkDeleted,
    DiagramCleared
}
