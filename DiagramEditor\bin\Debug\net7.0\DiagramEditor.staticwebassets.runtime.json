{"ContentRoots": ["D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\obj\\Debug\\net7.0\\scopedcss\\bundle\\", "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\DiagramEditor\\wwwroot\\", "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\", "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\wwwroot\\", "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams\\obj\\Debug\\net7.0\\scopedcss\\projectbundle\\"], "Root": {"Children": {"DiagramEditor.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "DiagramEditor.styles.css"}, "Patterns": null}, "favicon.png": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "favicon.png"}, "Patterns": null}, "css": {"Children": {"site.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/site.css"}, "Patterns": null}, "bootstrap": {"Children": {"bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/bootstrap/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/bootstrap/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "open-iconic": {"Children": {"FONT-LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/FONT-LICENSE"}, "Patterns": null}, "ICON-LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/ICON-LICENSE"}, "Patterns": null}, "README.md": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/README.md"}, "Patterns": null}, "font": {"Children": {"css": {"Children": {"open-iconic-bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "fonts": {"Children": {"open-iconic.eot": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.eot"}, "Patterns": null}, "open-iconic.otf": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.otf"}, "Patterns": null}, "open-iconic.svg": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.svg"}, "Patterns": null}, "open-iconic.ttf": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.ttf"}, "Patterns": null}, "open-iconic.woff": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.woff"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "_content": {"Children": {"MudBlazor": {"Children": {"MudBlazor.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "MudBlazor.min.css"}, "Patterns": null}, "MudBlazor.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "MudBlazor.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Z.Blazor.Diagrams": {"Children": {"default.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "default.styles.css"}, "Patterns": null}, "default.styles.min.css": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "default.styles.min.css"}, "Patterns": null}, "script.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "script.js"}, "Patterns": null}, "script.min.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "script.min.js"}, "Patterns": null}, "style.css": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "style.css"}, "Patterns": null}, "style.min.css": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "style.min.css"}, "Patterns": null}, "Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "Z.Blazor.Diagrams.bundle.scp.css"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 3, "Pattern": "**", "Depth": 2}]}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 1, "Pattern": "**", "Depth": 0}]}}