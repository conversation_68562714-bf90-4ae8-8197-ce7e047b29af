using Blazor.Diagrams.Core.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Blazor.Diagrams.Persistence;

/// <summary>
/// Entity Framework DbContext for diagram persistence
/// </summary>
public class DiagramDbContext : DbContext
{
    public DiagramDbContext(DbContextOptions<DiagramDbContext> options) : base(options)
    {
    }

    public DbSet<DiagramEntity> Diagrams { get; set; } = null!;
    public DbSet<NodeEntity> Nodes { get; set; } = null!;
    public DbSet<LinkEntity> Links { get; set; } = null!;
    public DbSet<PortEntity> Ports { get; set; } = null!;
    public DbSet<GroupEntity> Groups { get; set; } = null!;
    public DbSet<LinkVertexEntity> LinkVertices { get; set; } = null!;
    public DbSet<LinkLabelEntity> LinkLabels { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure DiagramEntity
        modelBuilder.Entity<DiagramEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.HasIndex(e => e.Name);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure NodeEntity
        modelBuilder.Entity<NodeEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DiagramId).IsRequired();
            entity.Property(e => e.NodeType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Title).HasMaxLength(200);
            
            entity.HasOne(e => e.Diagram)
                  .WithMany(d => d.Nodes)
                  .HasForeignKey(e => e.DiagramId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(e => e.Group)
                  .WithMany(g => g.ChildNodes)
                  .HasForeignKey(e => e.GroupId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasIndex(e => e.DiagramId);
            entity.HasIndex(e => e.GroupId);
        });

        // Configure LinkEntity
        modelBuilder.Entity<LinkEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DiagramId).IsRequired();
            entity.Property(e => e.LinkType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.SourceAnchorJson).IsRequired();
            entity.Property(e => e.TargetAnchorJson).IsRequired();
            entity.Property(e => e.Color).HasMaxLength(50);
            entity.Property(e => e.SelectedColor).HasMaxLength(50);
            
            entity.HasOne(e => e.Diagram)
                  .WithMany(d => d.Links)
                  .HasForeignKey(e => e.DiagramId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.DiagramId);
        });

        // Configure PortEntity
        modelBuilder.Entity<PortEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.NodeId).IsRequired();
            entity.Property(e => e.Alignment).IsRequired().HasMaxLength(50);
            
            entity.HasOne(e => e.Node)
                  .WithMany(n => n.Ports)
                  .HasForeignKey(e => e.NodeId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.NodeId);
        });

        // Configure GroupEntity
        modelBuilder.Entity<GroupEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DiagramId).IsRequired();
            entity.Property(e => e.GroupType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Title).HasMaxLength(200);
            
            entity.HasOne(e => e.Diagram)
                  .WithMany(d => d.Groups)
                  .HasForeignKey(e => e.DiagramId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(e => e.ParentGroup)
                  .WithMany(g => g.ChildGroups)
                  .HasForeignKey(e => e.ParentGroupId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasIndex(e => e.DiagramId);
            entity.HasIndex(e => e.ParentGroupId);
        });

        // Configure LinkVertexEntity
        modelBuilder.Entity<LinkVertexEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.LinkId).IsRequired();
            
            entity.HasOne(e => e.Link)
                  .WithMany(l => l.Vertices)
                  .HasForeignKey(e => e.LinkId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.LinkId);
            entity.HasIndex(e => new { e.LinkId, e.Order });
        });

        // Configure LinkLabelEntity
        modelBuilder.Entity<LinkLabelEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.LinkId).IsRequired();
            entity.Property(e => e.Content).IsRequired().HasMaxLength(500);
            
            entity.HasOne(e => e.Link)
                  .WithMany(l => l.Labels)
                  .HasForeignKey(e => e.LinkId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.LinkId);
        });
    }
}
