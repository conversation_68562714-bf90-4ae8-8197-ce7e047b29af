@using DiagramEditor.Plugins
@inject IPluginManager PluginManagerService
@inject ISnackbar Snackbar

<MudDrawer @bind-Open="IsOpen" 
           Anchor="Anchor.Right" 
           Elevation="2" 
           Variant="DrawerVariant.Temporary"
           Width="400px">
    <MudDrawerHeader>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Extension" />
            <MudText Typo="Typo.h6">Gerenciador de Plugins</MudText>
            <MudSpacer />
            <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="Close" Size="MudBlazor.Size.Small" />
        </MudStack>
    </MudDrawerHeader>
    
    <MudDrawerContainer>
        <MudContainer Class="pa-4">
            <MudStack Spacing="3">
                <!-- Estatísticas -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="4">
                                <MudStack AlignItems="AlignItems.Center" Spacing="1">
                                    <MudText Typo="Typo.h4" Color="Color.Primary">@LoadedPlugins.Count()</MudText>
                                    <MudText Typo="Typo.caption">Carregados</MudText>
                                </MudStack>
                            </MudItem>
                            <MudItem xs="4">
                                <MudStack AlignItems="AlignItems.Center" Spacing="1">
                                    <MudText Typo="Typo.h4" Color="Color.Success">@EnabledPlugins.Count()</MudText>
                                    <MudText Typo="Typo.caption">Ativos</MudText>
                                </MudStack>
                            </MudItem>
                            <MudItem xs="4">
                                <MudStack AlignItems="AlignItems.Center" Spacing="1">
                                    <MudText Typo="Typo.h4" Color="Color.Warning">@(LoadedPlugins.Count() - EnabledPlugins.Count())</MudText>
                                    <MudText Typo="Typo.caption">Inativos</MudText>
                                </MudStack>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>

                <!-- Ações -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudStack Spacing="2">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Primary" 
                                     StartIcon="@Icons.Material.Filled.Add"
                                     OnClick="LoadPluginAsync"
                                     FullWidth="true">
                                Carregar Plugin
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="@Icons.Material.Filled.Search"
                                     OnClick="DiscoverPluginsAsync"
                                     FullWidth="true">
                                Descobrir Plugins
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="@Icons.Material.Filled.Refresh"
                                     OnClick="RefreshPluginsAsync"
                                     FullWidth="true">
                                Atualizar Lista
                            </MudButton>
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Filtros -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudStack Spacing="2">
                            <MudTextField @bind-Value="SearchFilter" 
                                        Label="Buscar plugins" 
                                        Variant="Variant.Outlined"
                                        Adornment="Adornment.Start"
                                        AdornmentIcon="@Icons.Material.Filled.Search" />
                            
                            <MudSelect @bind-Value="CategoryFilter" 
                                     Label="Categoria" 
                                     Variant="Variant.Outlined">
                                <MudSelectItem Value="@((PluginCategory?)null)">Todas</MudSelectItem>
                                @foreach (var category in Enum.GetValues<PluginCategory>())
                                {
                                    <MudSelectItem Value="@category">@category</MudSelectItem>
                                }
                            </MudSelect>
                            
                            <MudSwitch @bind-Value="ShowOnlyEnabled" 
                                     Label="Mostrar apenas ativos" />
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Lista de Plugins -->
                <MudCard Elevation="1">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Plugins Instalados</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (FilteredPlugins.Any())
                        {
                            <MudStack Spacing="2">
                                @foreach (var plugin in FilteredPlugins)
                                {
                                    <MudPaper Class="pa-3" Elevation="0" Style="border: 1px solid var(--mud-palette-divider);">
                                        <MudStack Spacing="2">
                                            <!-- Header do Plugin -->
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@GetPluginIcon(plugin.Category)" 
                                                       Color="@GetPluginColor(plugin.Category)" />
                                                <MudStack Spacing="0">
                                                    <MudText Typo="Typo.subtitle1">@plugin.Name</MudText>
                                                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                                        <EMAIL> por @plugin.Author
                                                    </MudText>
                                                </MudStack>
                                                <MudSpacer />
                                                <MudSwitch Value="plugin.IsEnabled"
                                                         ValueChanged="@(async (bool enabled) => await TogglePluginAsync(plugin, enabled))"
                                                         Color="Color.Success" />
                                            </MudStack>

                                            <!-- Descrição -->
                                            <MudText Typo="Typo.body2">@plugin.Description</MudText>

                                            <!-- Categoria e Status -->
                                            <MudStack Row Spacing="1">
                                                <MudChip Size="MudBlazor.Size.Small" 
                                                       Color="@GetPluginColor(plugin.Category)">
                                                    @plugin.Category
                                                </MudChip>
                                                <MudChip Size="MudBlazor.Size.Small" 
                                                       Color="@(plugin.IsEnabled ? Color.Success : Color.Default)">
                                                    @(plugin.IsEnabled ? "Ativo" : "Inativo")
                                                </MudChip>
                                            </MudStack>

                                            <!-- Ações do Plugin -->
                                            <MudStack Row Spacing="1">
                                                <MudButton Size="MudBlazor.Size.Small" 
                                                         Variant="Variant.Text" 
                                                         StartIcon="@Icons.Material.Filled.Info"
                                                         OnClick="@(() => ShowPluginDetails(plugin))">
                                                    Detalhes
                                                </MudButton>
                                                
                                                @if (plugin.IsEnabled)
                                                {
                                                    <MudButton Size="MudBlazor.Size.Small" 
                                                             Variant="Variant.Text" 
                                                             StartIcon="@Icons.Material.Filled.Settings"
                                                             OnClick="@(() => ConfigurePlugin(plugin))">
                                                        Configurar
                                                    </MudButton>
                                                }
                                                
                                                <MudButton Size="MudBlazor.Size.Small" 
                                                         Variant="Variant.Text" 
                                                         Color="Color.Error"
                                                         StartIcon="@Icons.Material.Filled.Delete"
                                                         OnClick="@(() => UnloadPluginAsync(plugin))">
                                                    Remover
                                                </MudButton>
                                            </MudStack>
                                        </MudStack>
                                    </MudPaper>
                                }
                            </MudStack>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info" Dense="true">
                                @if (string.IsNullOrEmpty(SearchFilter) && CategoryFilter == null && !ShowOnlyEnabled)
                                {
                                    <text>Nenhum plugin carregado. Clique em "Carregar Plugin" para adicionar plugins.</text>
                                }
                                else
                                {
                                    <text>Nenhum plugin encontrado com os filtros aplicados.</text>
                                }
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>

                <!-- Marketplace (Futuro) -->
                <MudCard Elevation="1">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Marketplace</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudAlert Severity="Severity.Info" Dense="true">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                <MudIcon Icon="@Icons.Material.Filled.Store" />
                                <MudText>Marketplace de plugins em desenvolvimento</MudText>
                            </MudStack>
                        </MudAlert>
                    </MudCardContent>
                </MudCard>
            </MudStack>
        </MudContainer>
    </MudDrawerContainer>
</MudDrawer>

@code {
    [Parameter] public bool IsOpen { get; set; }
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }

    private string SearchFilter = "";
    private PluginCategory? CategoryFilter = null;
    private bool ShowOnlyEnabled = false;

    private IEnumerable<IPlugin> LoadedPlugins => PluginManagerService.LoadedPlugins;
    private IEnumerable<IPlugin> EnabledPlugins => PluginManagerService.EnabledPlugins;

    private IEnumerable<IPlugin> FilteredPlugins
    {
        get
        {
            var plugins = LoadedPlugins.AsEnumerable();

            if (!string.IsNullOrEmpty(SearchFilter))
            {
                plugins = plugins.Where(p => 
                    p.Name.Contains(SearchFilter, StringComparison.OrdinalIgnoreCase) ||
                    p.Description.Contains(SearchFilter, StringComparison.OrdinalIgnoreCase) ||
                    p.Author.Contains(SearchFilter, StringComparison.OrdinalIgnoreCase));
            }

            if (CategoryFilter.HasValue)
            {
                plugins = plugins.Where(p => p.Category == CategoryFilter.Value);
            }

            if (ShowOnlyEnabled)
            {
                plugins = plugins.Where(p => p.IsEnabled);
            }

            return plugins.OrderBy(p => p.Name);
        }
    }

    protected override void OnInitialized()
    {
        PluginManagerService.PluginLoaded += OnPluginChanged;
        PluginManagerService.PluginUnloaded += OnPluginChanged;
        PluginManagerService.PluginEnabled += OnPluginChanged;
        PluginManagerService.PluginDisabled += OnPluginChanged;
    }

    private void OnPluginChanged(IPlugin plugin)
    {
        InvokeAsync(StateHasChanged);
    }

    private async Task Close()
    {
        IsOpen = false;
        await IsOpenChanged.InvokeAsync(IsOpen);
    }

    private async Task LoadPluginAsync()
    {
        // Implementar seleção de arquivo de plugin
        Snackbar.Add("Funcionalidade de carregamento de plugin em desenvolvimento", Severity.Info);
    }

    private async Task DiscoverPluginsAsync()
    {
        try
        {
            var pluginsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Plugins");
            var discoveredPlugins = await PluginManagerService.DiscoverPluginsAsync(pluginsDirectory);
            
            Snackbar.Add($"Descobertos {discoveredPlugins.Count()} plugins", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao descobrir plugins: {ex.Message}", Severity.Error);
        }
    }

    private async Task RefreshPluginsAsync()
    {
        await InvokeAsync(StateHasChanged);
        Snackbar.Add("Lista de plugins atualizada", Severity.Success);
    }

    private async Task TogglePluginAsync(IPlugin plugin, bool enabled)
    {
        try
        {
            if (enabled)
            {
                await PluginManagerService.EnablePluginAsync(plugin.Id);
                Snackbar.Add($"Plugin '{plugin.Name}' ativado", Severity.Success);
            }
            else
            {
                await PluginManagerService.DisablePluginAsync(plugin.Id);
                Snackbar.Add($"Plugin '{plugin.Name}' desativado", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao alterar status do plugin: {ex.Message}", Severity.Error);
        }
    }

    private async Task UnloadPluginAsync(IPlugin plugin)
    {
        try
        {
            await PluginManagerService.UnloadPluginAsync(plugin.Id);
            Snackbar.Add($"Plugin '{plugin.Name}' removido", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao remover plugin: {ex.Message}", Severity.Error);
        }
    }

    private void ShowPluginDetails(IPlugin plugin)
    {
        // Implementar dialog de detalhes do plugin
        Snackbar.Add($"Detalhes do plugin '{plugin.Name}' em desenvolvimento", Severity.Info);
    }

    private void ConfigurePlugin(IPlugin plugin)
    {
        // Implementar configuração do plugin
        Snackbar.Add($"Configuração do plugin '{plugin.Name}' em desenvolvimento", Severity.Info);
    }

    private string GetPluginIcon(PluginCategory category)
    {
        return category switch
        {
            PluginCategory.Node => Icons.Material.Filled.AccountTree,
            PluginCategory.Link => Icons.Material.Filled.Link,
            PluginCategory.Tool => Icons.Material.Filled.Build,
            PluginCategory.Export => Icons.Material.Filled.FileDownload,
            PluginCategory.Import => Icons.Material.Filled.FileUpload,
            PluginCategory.Validation => Icons.Material.Filled.CheckCircle,
            PluginCategory.Theme => Icons.Material.Filled.Palette,
            PluginCategory.Collaboration => Icons.Material.Filled.People,
            PluginCategory.Integration => Icons.Material.Filled.IntegrationInstructions,
            PluginCategory.Utility => Icons.Material.Filled.Handyman,
            _ => Icons.Material.Filled.Extension
        };
    }

    private Color GetPluginColor(PluginCategory category)
    {
        return category switch
        {
            PluginCategory.Node => Color.Primary,
            PluginCategory.Link => Color.Secondary,
            PluginCategory.Tool => Color.Info,
            PluginCategory.Export => Color.Success,
            PluginCategory.Import => Color.Warning,
            PluginCategory.Validation => Color.Success,
            PluginCategory.Theme => Color.Secondary,
            PluginCategory.Collaboration => Color.Info,
            PluginCategory.Integration => Color.Primary,
            PluginCategory.Utility => Color.Dark,
            _ => Color.Default
        };
    }

    public void Dispose()
    {
        PluginManagerService.PluginLoaded -= OnPluginChanged;
        PluginManagerService.PluginUnloaded -= OnPluginChanged;
        PluginManagerService.PluginEnabled -= OnPluginChanged;
        PluginManagerService.PluginDisabled -= OnPluginChanged;
    }
}
