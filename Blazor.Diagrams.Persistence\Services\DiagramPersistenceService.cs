using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Persistence;
using Blazor.Diagrams.Core.Persistence.Serialization;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Persistence.Services;

/// <summary>
/// High-level service for persisting and loading complete diagrams
/// </summary>
public class DiagramPersistenceService : IDiagramPersistenceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IModelSerializer _serializer;
    private readonly DiagramDbContext _context;

    public DiagramPersistenceService(IUnitOfWork unitOfWork, IModelSerializer serializer, DiagramDbContext context)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _serializer = serializer ?? throw new ArgumentNullException(nameof(serializer));
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public async Task<string> SaveDiagramAsync(Diagram diagram, string? name = null, string? description = null, CancellationToken cancellationToken = default)
    {
        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        try
        {
            // Serialize the diagram
            var diagramEntity = _serializer.SerializeDiagram(diagram, name, description);
            
            // Save diagram entity
            await _unitOfWork.Diagrams.AddAsync(diagramEntity, cancellationToken);

            // Save all groups first (they might be referenced by nodes)
            foreach (var group in diagram.Groups)
            {
                var groupEntity = _serializer.SerializeGroup(group, diagramEntity.Id);
                await _unitOfWork.Groups.AddAsync(groupEntity, cancellationToken);
            }

            // Save all nodes with their ports
            foreach (var node in diagram.Nodes)
            {
                var nodeEntity = _serializer.SerializeNode(node, diagramEntity.Id);
                await _unitOfWork.Nodes.AddAsync(nodeEntity, cancellationToken);

                // Save ports for this node
                foreach (var port in node.Ports)
                {
                    var portEntity = _serializer.SerializePort(port, nodeEntity.Id);
                    await _unitOfWork.Ports.AddAsync(portEntity, cancellationToken);
                }
            }

            // Save all links with their vertices and labels
            foreach (var link in diagram.Links)
            {
                var linkEntity = _serializer.SerializeLink(link, diagramEntity.Id);
                await _unitOfWork.Links.AddAsync(linkEntity, cancellationToken);

                // Save vertices for this link
                for (int i = 0; i < link.Vertices.Count; i++)
                {
                    var vertex = link.Vertices[i];
                    var vertexEntity = new LinkVertexEntity
                    {
                        Id = vertex.Id,
                        LinkId = linkEntity.Id,
                        PositionX = vertex.Position.X,
                        PositionY = vertex.Position.Y,
                        Order = i,
                        Locked = vertex.Locked,
                        Visible = vertex.Visible,
                        Selected = vertex.Selected,
                        CreatedAt = vertex.CreatedAt,
                        UpdatedAt = vertex.UpdatedAt,
                        Version = vertex.Version
                    };
                    
                    // Note: We need to add LinkVertexEntity to the context directly since it's not a PortEntity
                    _context.LinkVertices.Add(vertexEntity);
                    await Task.CompletedTask;
                }

                // Save labels for this link
                foreach (var label in link.Labels)
                {
                    var labelEntity = new LinkLabelEntity
                    {
                        Id = label.Id,
                        LinkId = linkEntity.Id,
                        Content = label.Content,
                        Distance = label.Distance ?? 0.5,
                        OffsetX = label.Offset?.X ?? 0,
                        OffsetY = label.Offset?.Y ?? 0,
                        Locked = label.Locked,
                        Visible = label.Visible,
                        CreatedAt = label.CreatedAt,
                        UpdatedAt = label.UpdatedAt,
                        Version = label.Version
                    };
                    
                    // Note: We need to add LinkLabelEntity to the context directly since it's not a PortEntity
                    _context.LinkLabels.Add(labelEntity);
                    await Task.CompletedTask;
                }
            }

            await _unitOfWork.CommitAsync(cancellationToken);
            return diagramEntity.Id;
        }
        catch
        {
            await _unitOfWork.RollbackAsync(cancellationToken);
            throw;
        }
    }

    public async Task<Diagram?> LoadDiagramAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        // Load diagram entity
        var diagramEntity = await _unitOfWork.Diagrams.GetByIdAsync(diagramId, cancellationToken);
        if (diagramEntity == null)
            return null;

        // Load all related entities
        var nodeRepository = ((UnitOfWork)_unitOfWork).NodeRepository;
        var linkRepository = ((UnitOfWork)_unitOfWork).LinkRepository;

        var nodes = await nodeRepository.GetByDiagramIdWithPortsAsync(diagramId, cancellationToken);
        var links = await linkRepository.GetByDiagramIdWithDetailsAsync(diagramId, cancellationToken);
        var groups = await _unitOfWork.Groups.FindAsync(g => g.DiagramId == diagramId, cancellationToken);

        // Deserialize the diagram
        var diagram = _serializer.DeserializeDiagram(diagramEntity, nodes, links, groups);

        // Create node map for link deserialization
        var nodeMap = new Dictionary<string, Core.Models.NodeModel>();
        
        // Deserialize groups first
        foreach (var groupEntity in groups.OrderBy(g => g.CreatedAt))
        {
            var childNodes = nodes.Where(n => n.GroupId == groupEntity.Id);
            var deserializedChildNodes = childNodes.Select(n => _serializer.DeserializeNode(n, n.Ports));
            
            foreach (var childNode in deserializedChildNodes)
            {
                nodeMap[childNode.Id] = childNode;
            }
            
            var group = _serializer.DeserializeGroup(groupEntity, deserializedChildNodes);
            diagram.Groups.Add(group);
        }

        // Deserialize nodes
        foreach (var nodeEntity in nodes.Where(n => n.GroupId == null).OrderBy(n => n.CreatedAt))
        {
            var node = _serializer.DeserializeNode(nodeEntity, nodeEntity.Ports);
            nodeMap[node.Id] = node;
            diagram.Nodes.Add(node);
        }

        // Deserialize links
        foreach (var linkEntity in links.OrderBy(l => l.CreatedAt))
        {
            var link = _serializer.DeserializeLink(linkEntity, linkEntity.Vertices, linkEntity.Labels, nodeMap);
            diagram.Links.Add(link);
        }

        return diagram;
    }

    public async Task<IEnumerable<DiagramMetadata>> GetDiagramsMetadataAsync(CancellationToken cancellationToken = default)
    {
        var diagrams = await _unitOfWork.Diagrams.GetAllAsync(cancellationToken);
        var metadata = new List<DiagramMetadata>();

        foreach (var diagram in diagrams)
        {
            var nodeRepository = ((UnitOfWork)_unitOfWork).NodeRepository;
            var linkRepository = ((UnitOfWork)_unitOfWork).LinkRepository;

            var nodeCount = await nodeRepository.CountByDiagramIdAsync(diagram.Id, cancellationToken);
            var linkCount = await linkRepository.CountByDiagramIdAsync(diagram.Id, cancellationToken);
            var groupCount = await _unitOfWork.Groups.CountAsync(g => g.DiagramId == diagram.Id, cancellationToken);

            metadata.Add(new DiagramMetadata
            {
                Id = diagram.Id,
                Name = diagram.Name,
                Description = diagram.Description,
                CreatedAt = diagram.CreatedAt,
                UpdatedAt = diagram.UpdatedAt,
                NodeCount = nodeCount,
                LinkCount = linkCount,
                GroupCount = groupCount
            });
        }

        return metadata.OrderByDescending(m => m.UpdatedAt);
    }

    public async Task DeleteDiagramAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        try
        {
            // Delete in correct order due to foreign key constraints
            var nodeRepository = ((UnitOfWork)_unitOfWork).NodeRepository;
            var linkRepository = ((UnitOfWork)_unitOfWork).LinkRepository;

            await linkRepository.DeleteByDiagramIdAsync(diagramId, cancellationToken);
            await nodeRepository.DeleteByDiagramIdAsync(diagramId, cancellationToken);
            
            var groups = await _unitOfWork.Groups.FindAsync(g => g.DiagramId == diagramId, cancellationToken);
            foreach (var group in groups)
            {
                await _unitOfWork.Groups.DeleteAsync(group, cancellationToken);
            }

            await _unitOfWork.Diagrams.DeleteAsync(diagramId, cancellationToken);

            await _unitOfWork.CommitAsync(cancellationToken);
        }
        catch
        {
            await _unitOfWork.RollbackAsync(cancellationToken);
            throw;
        }
    }

    public async Task UpdateDiagramMetadataAsync(string diagramId, string? name = null, string? description = null, CancellationToken cancellationToken = default)
    {
        var diagram = await _unitOfWork.Diagrams.GetByIdAsync(diagramId, cancellationToken);
        if (diagram == null)
            throw new ArgumentException($"Diagram with ID {diagramId} not found", nameof(diagramId));

        if (name != null)
            diagram.Name = name;
        if (description != null)
            diagram.Description = description;

        await _unitOfWork.Diagrams.UpdateAsync(diagram, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<string> CloneDiagramAsync(string diagramId, string? newName = null, CancellationToken cancellationToken = default)
    {
        var originalDiagram = await LoadDiagramAsync(diagramId, cancellationToken);
        if (originalDiagram == null)
            throw new ArgumentException($"Diagram with ID {diagramId} not found", nameof(diagramId));

        var cloneName = newName ?? $"Copy of {(await _unitOfWork.Diagrams.GetByIdAsync(diagramId, cancellationToken))?.Name}";
        
        return await SaveDiagramAsync(originalDiagram, cloneName, "Cloned diagram", cancellationToken);
    }
}
