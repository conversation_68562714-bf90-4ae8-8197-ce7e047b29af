﻿@using DiagramEditor.Services
@using DiagramEditor.Components
@inherits LayoutComponentBase
@inject IThemeService ThemeService

<MudThemeProvider Theme="@ThemeService.CurrentTheme" />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">Editor de Diagramas</MudText>
        <MudSpacer />
        <ThemeSelector />
        <MudIconButton Icon="@Icons.Material.Filled.Settings" Color="Color.Inherit" />
        <MudIconButton Icon="@Icons.Material.Filled.Help" Color="Color.Inherit" />
    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="my-4 pt-4">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;

    protected override void OnInitialized()
    {
        ThemeService.ThemeChanged += StateHasChanged;
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    public void Dispose()
    {
        ThemeService.ThemeChanged -= StateHasChanged;
    }
}
