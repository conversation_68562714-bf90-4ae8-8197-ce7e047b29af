﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Z.Blazor.Diagrams.Core</id>
    <version>3.0.3</version>
    <authors>zHaytam</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>ZBD.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://blazor-diagrams.zhaytam.com/</projectUrl>
    <description>A fully customizable and extensible all-purpose diagrams library for Blazor</description>
    <tags>blazor diagrams diagramming svg drag</tags>
    <repository type="git" url="https://github.com/Blazor-Diagrams/Blazor.Diagrams" commit="492bec3b754def37506c1b93b466ab8b8c8b81ba" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Nanoid" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="SvgPathProperties" version="1.1.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="Nanoid" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="SvgPathProperties" version="1.1.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Nanoid" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="SvgPathProperties" version="1.1.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net9.0">
        <dependency id="Nanoid" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="SvgPathProperties" version="1.1.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams.Core\bin\Debug\net6.0\Blazor.Diagrams.Core.dll" target="lib\net6.0\Blazor.Diagrams.Core.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams.Core\bin\Debug\net7.0\Blazor.Diagrams.Core.dll" target="lib\net7.0\Blazor.Diagrams.Core.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams.Core\bin\Debug\net8.0\Blazor.Diagrams.Core.dll" target="lib\net8.0\Blazor.Diagrams.Core.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\src\Blazor.Diagrams.Core\bin\Debug\net9.0\Blazor.Diagrams.Core.dll" target="lib\net9.0\Blazor.Diagrams.Core.dll" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\ZBD.png" target="\ZBD.png" />
    <file src="D:\projects\MudBlazor\Blazor.Diagrams-vba-b20250627\README.md" target="\README.md" />
  </files>
</package>