{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"Blazor.Diagrams/3.0.3": {"dependencies": {"Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Web": "6.0.0", "Z.Blazor.Diagrams.Core": "3.0.3", "Blazor.Diagrams.Core": "*******"}, "runtime": {"Blazor.Diagrams.dll": {}}}, "Microsoft.AspNetCore.Authorization/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.Components/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "6.0.0", "Microsoft.AspNetCore.Components.Analyzers": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.Components.Analyzers/6.0.0": {}, "Microsoft.AspNetCore.Components.Forms/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.Components.Web/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "6.0.0", "Microsoft.AspNetCore.Components.Forms": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.JSInterop": "6.0.0", "System.IO.Pipelines": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.Metadata/6.0.0": {"runtime": {"lib/net6.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.JSInterop/6.0.0": {"runtime": {"lib/net6.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Nanoid/3.1.0": {"runtime": {"lib/netstandard2.0/Nanoid.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "SvgPathProperties/1.1.0": {"runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.IO.Pipelines/6.0.0": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "Z.Blazor.Diagrams.Core/3.0.3": {"dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.0"}, "runtime": {"Blazor.Diagrams.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Blazor.Diagrams.Core/*******": {"runtime": {"Blazor.Diagrams.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Blazor.Diagrams/3.0.3": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HPEhmNwINXzGVWirzmvVxx5/GkoaQJC7vKNIbhOUTzgnRHEf2zO6S2s4lMw3TPFWBGcAlfn6Ta0cVB9f15QC8w==", "path": "microsoft.aspnetcore.authorization/6.0.0", "hashPath": "microsoft.aspnetcore.authorization.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETks/RMYyiC91P89jL9XNEwTeYlpyzt2+Z584v70h+B9//BO0cc68gAlhz3E0v/9h4NqCSHbTlopvMvj0qxnEg==", "path": "microsoft.aspnetcore.components/6.0.0", "hashPath": "microsoft.aspnetcore.components.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2<PERSON><PERSON><PERSON>hhUAJXYgvrePJNXzhJfM5u6MAIuu4Nho6LEjQliMtAQ6NcdtYxkezoFxqTrgT1fZR0CSYAxp7k41q6PVQ==", "path": "microsoft.aspnetcore.components.analyzers/6.0.0", "hashPath": "microsoft.aspnetcore.components.analyzers.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-POjep/9qy79PD/q/5Gai7Xsg6Oybmo10+bVsCkoTrfu1kAO8dWgbQgJWH8I/VMfOPZY/dPgfo+BitqEriWMuKQ==", "path": "microsoft.aspnetcore.components.forms/6.0.0", "hashPath": "microsoft.aspnetcore.components.forms.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EvTZEhXCqcIxEs01ScF0ts/CyMOzXQIFfcgwTeLae7dQV7WnPEFhqrlD+1l78lA/h+Uer1RvaRbkAScGE/0tyQ==", "path": "microsoft.aspnetcore.components.web/6.0.0", "hashPath": "microsoft.aspnetcore.components.web.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-giBCvjANLIOqC+DJ84zCkCQnE4ebRTrgVyBe5e0gG3/F8GYzdqSSdtMSAvbOkBQsf0F8dySBPSM59vX6ksOeQg==", "path": "microsoft.aspnetcore.metadata/6.0.0", "hashPath": "microsoft.aspnetcore.metadata.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.JSInterop/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M7NyTgOY0vhozRtppvxUDJblgPWBUg6ioJ2vpaKpXVYILB0No8Vrv4HPis+ig5BzaOWd3PR9D3oTovYKGBt3mw==", "path": "microsoft.jsinterop/6.0.0", "hashPath": "microsoft.jsinterop.6.0.0.nupkg.sha512"}, "Nanoid/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-5vhxZ+1iVH213boPsR3KYQXWObZQcaGvLU9ytTroWJFeVzV2EFAtGgrmMpb8BCxd+KWeBASniZLzd3UQ0tkhRQ==", "path": "nanoid/3.1.0", "hashPath": "nanoid.3.1.0.nupkg.sha512"}, "SvgPathProperties/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9EE1r+9CkULhO5VYtCKnppSzMyXSr4ziATif2u+AUvHz9vAX4WHmHH52dWzo92QyELl2GWvopdtQLkCheO2OsA==", "path": "svgpathproperties/1.1.0", "hashPath": "svgpathproperties.1.1.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mXX66shZ4xLlI3vNLaJ0lt8OIZdmXTvIqXRdQX5HLVGSkLhINLsVhyZuX2UdRFnOGkqnwmMUs40pIIQ7mna4+A==", "path": "system.io.pipelines/6.0.0", "hashPath": "system.io.pipelines.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "serviceable": false, "sha512": ""}, "Blazor.Diagrams.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}