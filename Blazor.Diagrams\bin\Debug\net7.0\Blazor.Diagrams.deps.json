{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"Blazor.Diagrams/3.0.3": {"dependencies": {"Microsoft.AspNetCore.Components": "7.0.14", "Microsoft.AspNetCore.Components.Web": "7.0.14", "Z.Blazor.Diagrams.Core": "3.0.3", "Blazor.Diagrams.Core": "*******"}, "runtime": {"Blazor.Diagrams.dll": {}}}, "Microsoft.AspNetCore.Authorization/7.0.14": {"dependencies": {"Microsoft.AspNetCore.Metadata": "7.0.14", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.AspNetCore.Components/7.0.14": {"dependencies": {"Microsoft.AspNetCore.Authorization": "7.0.14", "Microsoft.AspNetCore.Components.Analyzers": "7.0.14"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.AspNetCore.Components.Analyzers/7.0.14": {}, "Microsoft.AspNetCore.Components.Forms/7.0.14": {"dependencies": {"Microsoft.AspNetCore.Components": "7.0.14"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.AspNetCore.Components.Web/7.0.14": {"dependencies": {"Microsoft.AspNetCore.Components": "7.0.14", "Microsoft.AspNetCore.Components.Forms": "7.0.14", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.JSInterop": "7.0.14", "System.IO.Pipelines": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.AspNetCore.Metadata/7.0.14": {"runtime": {"lib/net7.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.JSInterop/7.0.14": {"runtime": {"lib/net7.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.1423.52316"}}}, "Nanoid/3.1.0": {"runtime": {"lib/netstandard2.0/Nanoid.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "SvgPathProperties/1.1.2": {"runtime": {"lib/netstandard2.0/SvgPathProperties.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.IO.Pipelines/7.0.0": {"runtime": {"lib/net7.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Z.Blazor.Diagrams.Core/3.0.3": {"dependencies": {"Nanoid": "3.1.0", "SvgPathProperties": "1.1.2"}, "runtime": {"Blazor.Diagrams.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Blazor.Diagrams.Core/*******": {"runtime": {"Blazor.Diagrams.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Blazor.Diagrams/3.0.3": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4uM+ZIljMV2hF+LC4qRjswLsG21EO5pH07j9ec0H2eXFHlNoC1qIagHxgoQA2spkImqrBgIhWDog6VRQODTNw==", "path": "microsoft.aspnetcore.authorization/7.0.14", "hashPath": "microsoft.aspnetcore.authorization.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Components/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-UyCH0KPXTXQVf3/IzFAGLmHVN6HjLB1JjRQhyOuTIG3bJVMaKjg3zi0F8z3bAH16S5iMaNNd8dqpby8cd98aSg==", "path": "microsoft.aspnetcore.components/7.0.14", "hashPath": "microsoft.aspnetcore.components.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-qdhspIGt1B04iqIhxrch4ZJ/l+pjDMaibHdgaANbLVZBaV9qfc66TcGh82+dz2wyn2hZdYRlAw1oe4WIQpK5CQ==", "path": "microsoft.aspnetcore.components.analyzers/7.0.14", "hashPath": "microsoft.aspnetcore.components.analyzers.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-rzCBjZbuGIi+u4JYgw8sZ2cmBpm8hPwvCRGwCLmXFStSMCTZ9zogxVGFNXCUr2SvckOMZsm6liyOnddEUWv9Ng==", "path": "microsoft.aspnetcore.components.forms/7.0.14", "hashPath": "microsoft.aspnetcore.components.forms.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-6NUZNP2SulUqj4kQeap6AzlZWfMSAGoi2M/RKS2m/t2sy0J097IwNEjdLXpF4krWuNbSsCq3QvcVhEt563fvVQ==", "path": "microsoft.aspnetcore.components.web/7.0.14", "hashPath": "microsoft.aspnetcore.components.web.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-c5QBvWR294bUxA5bePGHjO6y2/hK3yF6WCGH+FVMnG5kjpD1pfgBmgnQUmpxkL34Ql26jlWUyfu/7rsBu4nJtw==", "path": "microsoft.aspnetcore.metadata/7.0.14", "hashPath": "microsoft.aspnetcore.metadata.7.0.14.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "path": "microsoft.extensions.logging.abstractions/7.0.1", "hashPath": "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.JSInterop/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-5+YxHnaDo5p+o+4FeQAitO/Sehr/hOo9a+psA2PT+hdup0OpKjuLuXTqqlrC96Yl57/Y1N24GeY8x/RrJ+k5MQ==", "path": "microsoft.jsinterop/7.0.14", "hashPath": "microsoft.jsinterop.7.0.14.nupkg.sha512"}, "Nanoid/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-5vhxZ+1iVH213boPsR3KYQXWObZQcaGvLU9ytTroWJFeVzV2EFAtGgrmMpb8BCxd+KWeBASniZLzd3UQ0tkhRQ==", "path": "nanoid/3.1.0", "hashPath": "nanoid.3.1.0.nupkg.sha512"}, "SvgPathProperties/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oqCX5+CxcLfC1lC6gfzOSM+WOmfwn4V7yzMHQfofi+QmYoY+Hq5oye6BgnqhHC+3BSsiadLSOnfYxpcv/enkcw==", "path": "svgpathproperties/1.1.2", "hashPath": "svgpathproperties.1.1.2.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "Z.Blazor.Diagrams.Core/3.0.3": {"type": "project", "serviceable": false, "sha512": ""}, "Blazor.Diagrams.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}