@using DiagramEditor.Models
@using Blazor.Diagrams.Core.Models
@inherits ComponentBase

<div class="rectangle-node" 
     style="width: @(Node.Size?.Width ?? 120)px; 
            height: @(Node.Size?.Height ?? 60)px; 
            background-color: @Node.BackgroundColor; 
            border: @(Node.BorderWidth)px solid @Node.BorderColor;
            color: @Node.TextColor;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;"
     @onmouseenter="OnMouseEnter"
     @onmouseleave="OnMouseLeave">
    
    @if (!string.IsNullOrEmpty(Node.Title))
    {
        <span>@Node.Title</span>
    }
</div>

<style>
    .rectangle-node:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .rectangle-node.selected {
        border-color: #FF5722 !important;
        border-width: 3px !important;
    }
</style>

@code {
    [Parameter] public RectangleNodeModel Node { get; set; } = null!;

    private void OnMouseEnter()
    {
        // Adicionar efeito hover se necessário
    }

    private void OnMouseLeave()
    {
        // Remover efeito hover se necessário
    }
}
