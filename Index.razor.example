@page "/"

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-16">
    <MudText Typo="Typo.h3" Align="Align.Center" GutterBottom="true">Editor de Diagramas</MudText>
    <MudText Typo="Typo.subtitle1" Align="Align.Center" Class="mb-8">Uma aplicação para criar, editar e gerenciar diagramas com Blazor Server e MudBlazor</MudText>

    <MudGrid Justify="Justify.Center" Class="mt-8">
        <MudItem xs="12" sm="6" md="4">
            <MudCard Elevation="4" Class="h-100">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h5">Criar Diagrama</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudIcon Icon="@Icons.Material.Filled.Add" Color="Color.Primary" />
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudText>Crie um novo diagrama do zero com nós personalizados e links ortogonais.</MudText>
                </MudCardContent>
                <MudCardActions>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Link="/diagram-editor" FullWidth="true">Começar</MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="4">
            <MudCard Elevation="4" Class="h-100">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h5">Diagramas Salvos</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudIcon Icon="@Icons.Material.Filled.List" Color="Color.Secondary" />
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudText>Visualize, edite ou exclua diagramas que você salvou anteriormente.</MudText>
                </MudCardContent>
                <MudCardActions>
                    <MudButton Variant="Variant.Filled" Color="Color.Secondary" Link="/saved-diagrams" FullWidth="true">Ver Diagramas</MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
    </MudGrid>

    <MudPaper Elevation="0" Class="mt-16 pa-4 mud-background-gray rounded-lg">
        <MudText Typo="Typo.h5" Class="mb-4">Recursos Principais</MudText>
        <MudList>
            <MudListItem Icon="@Icons.Material.Filled.Edit">
                <MudText>Crie diagramas com nós personalizáveis e links ortogonais</MudText>
            </MudListItem>
            <MudListItem Icon="@Icons.Material.Filled.Palette">
                <MudText>Personalize cores, espessuras de linha e estilos de texto</MudText>
            </MudListItem>
            <MudListItem Icon="@Icons.Material.Filled.Save">
                <MudText>Salve e carregue diagramas do banco de dados</MudText>
            </MudListItem>
            <MudListItem Icon="@Icons.Material.Filled.ViewQuilt">
                <MudText>Interface moderna e responsiva com MudBlazor</MudText>
            </MudListItem>
        </MudList>
    </MudPaper>
</MudContainer>