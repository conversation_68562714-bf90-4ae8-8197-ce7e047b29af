{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "DiagramEditor.styles.css", "AssetFile": "DiagramEditor.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001020408163"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "ETag", "Value": "W/\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}]}, {"Route": "DiagramEditor.styles.css", "AssetFile": "DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}]}, {"Route": "DiagramEditor.styles.css.gz", "AssetFile": "DiagramEditor.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI="}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css", "AssetFile": "DiagramEditor.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001020408163"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "ETag", "Value": "W/\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}, {"Name": "label", "Value": "DiagramEditor.styles.css"}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css", "AssetFile": "DiagramEditor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Link", "Value": "<_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-VUq3dTDQoHi1TjeU13cvOjjHj7YXHoM3E61DLMnKWn4="}, {"Name": "label", "Value": "DiagramEditor.styles.css"}]}, {"Route": "DiagramEditor.v3ikh3owht.styles.css.gz", "AssetFile": "DiagramEditor.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "979"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v3ikh3owht"}, {"Name": "integrity", "Value": "sha256-lz1+R+iZ/oPx/Sxec9dkNjBWprylPV9pdcoK9s6PDbI="}, {"Name": "label", "Value": "DiagramEditor.styles.css.gz"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "508754"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018156070"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=\""}, {"Name": "ETag", "Value": "W/\"O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55077"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9xWACzuq66PbY4HHqDlGxdtFdu+r4ep5rJjwQYt+oj8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44027"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Dec 2023 22:14:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000103029054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=\""}, {"Name": "ETag", "Value": "W/\"+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X+TBN0rECy3PTUOU6BN3wadD9MRZWjOiTjVaLaGo/dg="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "ETag", "Value": "W/\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxhkjam3jz"}, {"Name": "integrity", "Value": "sha256-+AaSp0/9+UkcPp4DHuTFoG9H/G9xif3knedtBn+wog8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/Z.Blazor.Diagrams.bundle.scp.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001191895113"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "ETag", "Value": "W/\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3445"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.kr4r5y5l5h.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kr4r5y5l5h"}, {"Name": "integrity", "Value": "sha256-bL2u3b3O2PREKHt+TZVULV7Waa6axZg7gBeD0vS8YyI="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.6pwzqlbbfs.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pwzqlbbfs"}, {"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001468428781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "ETag", "Value": "W/\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2533"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc="}]}, {"Route": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/default.styles.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "680"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aW3CYRQ8ynAW6RGyRRnStsIRhfdUXjhZyYg9/AMAzE="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.c5cp0u3gkb.js.gz", "AssetFile": "_content/Z.Blazor.Diagrams/script.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c5cp0u3gkb"}, {"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.js.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001533742331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "ETag", "Value": "W/\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2034"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.js.gz", "AssetFile": "_content/Z.Blazor.Diagrams/script.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g7l0a+4mSRl0nLRUffcK3m1vAupdxWOssBfdz0OerJQ="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.js.gz", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js"}]}, {"Route": "_content/Z.Blazor.Diagrams/script.min.u872bpsf3j.js.gz", "AssetFile": "_content/Z.Blazor.Diagrams/script.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/script.min.js.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.9j2o0uhpet.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j2o0uhpet"}, {"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.css.gz"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001956947162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "ETag", "Value": "W/\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1939"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "510"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HmS5POWLOkAPoBs72RlGPoyY7JzDVfsr6FYJ2WlBOis="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Fri, 27 Jun 2025 14:31:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css"}]}, {"Route": "_content/Z.Blazor.Diagrams/style.min.kjpcwcpl0m.css.gz", "AssetFile": "_content/Z.Blazor.Diagrams/style.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}, {"Name": "label", "Value": "_content/Z.Blazor.Diagrams/style.min.css.gz"}]}, {"Route": "css/blazor-diagrams.min.css", "AssetFile": "css/blazor-diagrams.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.css", "AssetFile": "css/blazor-diagrams.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}]}, {"Route": "css/blazor-diagrams.min.css.gz", "AssetFile": "css/blazor-diagrams.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css", "AssetFile": "css/blazor-diagrams.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002386634845"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "ETag", "Value": "W/\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "css/blazor-diagrams.min.css"}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css", "AssetFile": "css/blazor-diagrams.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1327"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA="}, {"Name": "label", "Value": "css/blazor-diagrams.min.css"}]}, {"Route": "css/blazor-diagrams.min.kjpcwcpl0m.css.gz", "AssetFile": "css/blazor-diagrams.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "418"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjpcwcpl0m"}, {"Name": "integrity", "Value": "sha256-hYgrevPb/VTAtX9c3aTAO81oA3AJxOf8V4qUhsHYcfY="}, {"Name": "label", "Value": "css/blazor-diagrams.min.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041925205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "ETag", "Value": "W/\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041925205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "ETag", "Value": "W/\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map.gz"}]}, {"Route": "css/bootstrap/bootstrap.min.css.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23851"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vS+1ufj92SdpGYtqXPqzcZfZweLNck5DdvtrYoo0Ruc="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013397104"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map.gz", "AssetFile": "css/bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74642"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-epiz+IkftKdCVrNwM3M3b4/Co3mxoqDpjOfMrHfv/7E="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md", "AssetFile": "css/open-iconic/README.md.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000673400673"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "ETag", "Value": "W/\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.5bzwdl5l6x.md.gz", "AssetFile": "css/open-iconic/README.md.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5bzwdl5l6x"}, {"Name": "integrity", "Value": "sha256-cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q="}, {"Name": "label", "Value": "css/open-iconic/README.md.gz"}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000673400673"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "ETag", "Value": "W/\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3655"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waukoLqsiIAw/nXpsKkTHnhImmcPijcBEd2lzqzJNN0="}]}, {"Route": "css/open-iconic/README.md.gz", "AssetFile": "css/open-iconic/README.md.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1484"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cfRN4Q9ELe7iE5XSAxcqFpH53WR0Fjin0dru915oq0Q="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000474608448"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "ETag", "Value": "W/\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css.gz", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000474608448"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "ETag", "Value": "W/\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZkI1/dKtx7cv+T8mJHA/4zRJzY3H+i9h3I+lQMlQ5/I="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076528660"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "ETag", "Value": "W/\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf.gz", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000074266617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "ETag", "Value": "W/\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg.gz", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "integrity", "Value": "sha256-27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg.gz"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000074266617"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "ETag", "Value": "W/\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg.gz", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13464"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-27Y8oN5HwPm3Lnq4ndc6QKUgTo4gMMgp80Rsx7BJueo="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076528660"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "ETag", "Value": "W/\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf.gz", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13066"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-hIC7IZDzsN/8/x7Q4PWDuy8dGV5BpkpIOTA6gK9OXok="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf.gz"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000603500302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1656"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o=\""}, {"Name": "ETag", "Value": "W/\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2978"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1656"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o="}]}, {"Route": "css/site.zh9s866nx2.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000603500302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1656"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o=\""}, {"Name": "ETag", "Value": "W/\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh9s866nx2"}, {"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.zh9s866nx2.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2978"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh9s866nx2"}, {"Name": "integrity", "Value": "sha256-Fx+c1youNLgejKLBM3S2fD6fcDOSv/D7tNQKm5gws6U="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.zh9s866nx2.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1656"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh9s866nx2"}, {"Name": "integrity", "Value": "sha256-L715j+XAyMUxRRdXM7qZJuK6uhQrV2JMoMe++0pdb8o="}, {"Name": "label", "Value": "css/site.css.gz"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:10:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/blazor-diagrams.min.js", "AssetFile": "js/blazor-diagrams.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.js", "AssetFile": "js/blazor-diagrams.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}]}, {"Route": "js/blazor-diagrams.min.js.gz", "AssetFile": "js/blazor-diagrams.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js", "AssetFile": "js/blazor-diagrams.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001919385797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "ETag", "Value": "W/\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "js/blazor-diagrams.min.js"}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js", "AssetFile": "js/blazor-diagrams.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:43:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8="}, {"Name": "label", "Value": "js/blazor-diagrams.min.js"}]}, {"Route": "js/blazor-diagrams.min.u872bpsf3j.js.gz", "AssetFile": "js/blazor-diagrams.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u872bpsf3j"}, {"Name": "integrity", "Value": "sha256-pOMlbbebfCqhg49CklANefwmzR2m7YXGI48GLxSrskY="}, {"Name": "label", "Value": "js/blazor-diagrams.min.js.gz"}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js", "AssetFile": "js/diagram-export.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000582411182"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "ETag", "Value": "W/\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}, {"Name": "label", "Value": "js/diagram-export.js"}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js", "AssetFile": "js/diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}, {"Name": "label", "Value": "js/diagram-export.js"}]}, {"Route": "js/diagram-export.hz8bn5rrj8.js.gz", "AssetFile": "js/diagram-export.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hz8bn5rrj8"}, {"Name": "integrity", "Value": "sha256-tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o="}, {"Name": "label", "Value": "js/diagram-export.js.gz"}]}, {"Route": "js/diagram-export.js", "AssetFile": "js/diagram-export.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000582411182"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "ETag", "Value": "W/\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.js", "AssetFile": "js/diagram-export.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 18:42:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PR3F2AjJ9TV4OX8syDuYxozFQpdI4pcXYIj4sdT4kfo="}]}, {"Route": "js/diagram-export.js.gz", "AssetFile": "js/diagram-export.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1716"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:46:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tJlO18WHk5dBW8doU+EUjleaxNeISsKMLhesUPHny3o="}]}, {"Route": "js/responsive.js", "AssetFile": "js/responsive.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000343170899"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "ETag", "Value": "W/\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.js", "AssetFile": "js/responsive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:47:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}]}, {"Route": "js/responsive.js.gz", "AssetFile": "js/responsive.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8="}]}, {"Route": "js/responsive.wkr7rr9ybj.js", "AssetFile": "js/responsive.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000343170899"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "ETag", "Value": "W/\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}, {"Name": "label", "Value": "js/responsive.js"}]}, {"Route": "js/responsive.wkr7rr9ybj.js", "AssetFile": "js/responsive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:47:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "integrity", "Value": "sha256-oCGEReRMQHDxsjv29PuYMTWUJVSebzbjRMHHRb7uJSM="}, {"Name": "label", "Value": "js/responsive.js"}]}, {"Route": "js/responsive.wkr7rr9ybj.js.gz", "AssetFile": "js/responsive.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2913"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 10 Jul 2025 01:50:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkr7rr9ybj"}, {"Name": "integrity", "Value": "sha256-5e4K5lko2yWsGkQAGkeXZd9L9Vt5aIVR7We5Zi/htA8="}, {"Name": "label", "Value": "js/responsive.js.gz"}]}]}