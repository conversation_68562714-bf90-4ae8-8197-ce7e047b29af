﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>default.styles.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kr4r5y5l5h</Fingerprint>
      <Integrity>IDKDuKfRCVCXOH3f/Z+NueVf5u+0YSUCGOLYdG+ZYf8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>3445</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>default.styles.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6pwzqlbbfs</Fingerprint>
      <Integrity>tjG7h09kCbOtLws3pLFB95nmOYxMZl7c8jbGPTarGBc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>2533</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\default.styles.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>script.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>c5cp0u3gkb</Fingerprint>
      <Integrity>QI5d3jQ5r735qncQ4geb3Y+zDoaSz75G44z9f4XpdbY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>2034</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>script.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>u872bpsf3j</Fingerprint>
      <Integrity>LgAw9yB0DF0MNdupxctpNfEU7NoB56YJnh9QpuwRcI8=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>1071</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\script.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>style.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>9j2o0uhpet</Fingerprint>
      <Integrity>K1X5IFVjQT+BsPfJNs8zGs0TciYMi3kijzYxSFO/zI4=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>1939</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>style.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>kjpcwcpl0m</Fingerprint>
      <Integrity>UKzIp+VqUElrrWqYXITbK2mVVp6d5hx2LP+pnMfozLA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>1327</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:31:03 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\style.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css'))">
      <SourceType>Package</SourceType>
      <SourceId>Z.Blazor.Diagrams</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Z.Blazor.Diagrams</BasePath>
      <RelativePath>Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>Reference</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName>ScopedCss</AssetTraitName>
      <AssetTraitValue>ProjectBundle</AssetTraitValue>
      <Fingerprint>wxhkjam3jz</Fingerprint>
      <Integrity>1BJ1LG+GEIBfnUdG7OaCA0tmgieFYeboHebcWeOmmiI=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>81</FileLength>
      <LastWriteTime>Fri, 27 Jun 2025 14:38:07 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Z.Blazor.Diagrams.wxhkjam3jz.bundle.scp.css'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>