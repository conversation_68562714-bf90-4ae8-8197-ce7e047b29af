using DiagramEditor.Services;
using DiagramEditor.Models;
using Blazor.Diagrams.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DiagramEditor.Tests;

public class IntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly DiagramDbContext _context;
    private readonly IDiagramRepository _repository;

    public IntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Configurar banco de dados em memória para testes
        services.AddDbContext<DiagramDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        services.AddScoped<IDiagramRepository, DiagramRepository>();
        services.AddLogging(builder => builder.AddConsole());

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<DiagramDbContext>();
        _repository = _serviceProvider.GetRequiredService<IDiagramRepository>();

        // Garantir que o banco está criado
        _context.Database.EnsureCreated();
    }

    [Fact]
    public async Task Repository_CreateAndRetrieve_WorksCorrectly()
    {
        // Arrange
        var diagramData = new DiagramData
        {
            Id = "test-diagram-1",
            Name = "Test Diagram",
            Data = "{\"nodes\":[], \"links\":[]}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Act
        await _repository.CreateAsync(diagramData);
        var retrieved = await _repository.GetByIdAsync("test-diagram-1");

        // Assert
        Assert.NotNull(retrieved);
        Assert.Equal("Test Diagram", retrieved.Name);
        Assert.Equal("{\"nodes\":[], \"links\":[]}", retrieved.Data);
    }

    [Fact]
    public async Task Repository_GetAll_ReturnsAllDiagrams()
    {
        // Arrange
        var diagram1 = new DiagramData
        {
            Id = "test-1",
            Name = "Diagram 1",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var diagram2 = new DiagramData
        {
            Id = "test-2",
            Name = "Diagram 2",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _repository.CreateAsync(diagram1);
        await _repository.CreateAsync(diagram2);

        // Act
        var allDiagrams = await _repository.GetAllAsync();

        // Assert
        Assert.True(allDiagrams.Count() >= 2);
        Assert.Contains(allDiagrams, d => d.Name == "Diagram 1");
        Assert.Contains(allDiagrams, d => d.Name == "Diagram 2");
    }

    [Fact]
    public async Task Repository_Update_ModifiesExistingDiagram()
    {
        // Arrange
        var originalDiagram = new DiagramData
        {
            Id = "update-test",
            Name = "Original Name",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _repository.CreateAsync(originalDiagram);

        // Act
        originalDiagram.Name = "Updated Name";
        originalDiagram.Data = "{\"updated\": true}";
        await _repository.UpdateAsync(originalDiagram);

        var updated = await _repository.GetByIdAsync("update-test");

        // Assert
        Assert.NotNull(updated);
        Assert.Equal("Updated Name", updated.Name);
        Assert.Equal("{\"updated\": true}", updated.Data);
    }

    [Fact]
    public async Task Repository_Delete_RemovesDiagram()
    {
        // Arrange
        var diagramToDelete = new DiagramData
        {
            Id = "delete-test",
            Name = "To Delete",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _repository.CreateAsync(diagramToDelete);

        // Act
        await _repository.DeleteAsync("delete-test");
        var deleted = await _repository.GetByIdAsync("delete-test");

        // Assert
        Assert.Null(deleted);
    }

    [Fact]
    public async Task Repository_GetByIdAsync_WithNonExistentId_ReturnsNull()
    {
        // Act
        var result = await _repository.GetByIdAsync("non-existent-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task Repository_CreateAsync_WithDuplicateId_ThrowsException()
    {
        // Arrange
        var diagram1 = new DiagramData
        {
            Id = "duplicate-id",
            Name = "First",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var diagram2 = new DiagramData
        {
            Id = "duplicate-id",
            Name = "Second",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _repository.CreateAsync(diagram1);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _repository.CreateAsync(diagram2));
    }

    [Fact]
    public async Task Repository_UpdateAsync_WithNonExistentId_ThrowsException()
    {
        // Arrange
        var nonExistentDiagram = new DiagramData
        {
            Id = "non-existent",
            Name = "Test",
            Data = "{}",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _repository.UpdateAsync(nonExistentDiagram));
    }

    [Fact]
    public async Task Repository_DeleteAsync_WithNonExistentId_DoesNotThrow()
    {
        // Act & Assert - Should not throw exception
        await _repository.DeleteAsync("non-existent-id");
    }

    [Fact]
    public async Task Repository_ConcurrentOperations_HandleCorrectly()
    {
        // Arrange
        var tasks = new List<Task>();
        
        // Act - Criar múltiplos diagramas concorrentemente
        for (int i = 0; i < 10; i++)
        {
            var index = i;
            tasks.Add(Task.Run(async () =>
            {
                var diagram = new DiagramData
                {
                    Id = $"concurrent-{index}",
                    Name = $"Concurrent Diagram {index}",
                    Data = "{}",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.CreateAsync(diagram);
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allDiagrams = await _repository.GetAllAsync();
        var concurrentDiagrams = allDiagrams.Where(d => d.Id.StartsWith("concurrent-"));
        Assert.Equal(10, concurrentDiagrams.Count());
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}
