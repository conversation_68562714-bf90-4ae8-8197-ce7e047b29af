@page "/saved-diagrams"
@using Blazor.Diagrams.Core.Persistence
@inject global::DiagramEditor.Services.IDiagramRepository DiagramRepository
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject IDialogService DialogService

<PageTitle>Diagramas Salvos</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h4" Class="mb-4">Diagramas Salvos</MudText>

    <MudPaper Elevation="1" Class="pa-4">
        <MudDataGrid T="global::DiagramEditor.Services.DiagramData" Items="@diagrams" Loading="@loading" Hover="true" Striped="true">
            <Columns>
                <PropertyColumn Property="x => x.Name" Title="Nome" />
                <PropertyColumn Property="x => x.CreatedAt" Title="Criado em" Format="dd/MM/yyyy HH:mm" />
                <PropertyColumn Property="x => x.UpdatedAt" Title="Atualizado em" Format="dd/MM/yyyy HH:mm" />
                <TemplateColumn Title="Ações" CellClass="d-flex justify-end">
                    <CellTemplate>
                        <MudStack Row>
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Primary"
                                     Size="MudBlazor.Size.Small"
                                     StartIcon="@Icons.Material.Filled.Edit"
                                     OnClick="@(() => EditDiagram(context.Item))">
                                Editar
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Secondary"
                                     Size="MudBlazor.Size.Small"
                                     StartIcon="@Icons.Material.Filled.ContentCopy"
                                     OnClick="@(() => DuplicateDiagram(context.Item))">
                                Duplicar
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Error"
                                     Size="MudBlazor.Size.Small"
                                     StartIcon="@Icons.Material.Filled.Delete"
                                     OnClick="@(() => DeleteDiagram(context.Item))">
                                Excluir
                            </MudButton>
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>

        @if (!diagrams.Any() && !loading)
        {
            <MudAlert Severity="Severity.Info" Class="mt-4">
                Nenhum diagrama salvo encontrado. <MudLink Href="/diagram-editor">Criar novo diagrama</MudLink>
            </MudAlert>
        }
    </MudPaper>

    <MudFab Color="Color.Primary"
           StartIcon="@Icons.Material.Filled.Add"
           Class="fixed-fab"
           OnClick="CreateNewDiagram" />
</MudContainer>

<style>
    .fixed-fab {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }
</style>

@code {
    private List<global::DiagramEditor.Services.DiagramData> diagrams = new();
    private bool loading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDiagrams();
    }

    private async Task LoadDiagrams()
    {
        loading = true;
        try
        {
            diagrams = (await DiagramRepository.GetAllAsync()).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar diagramas: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private void CreateNewDiagram()
    {
        Navigation.NavigateTo("/diagram-editor");
    }

    private void EditDiagram(global::DiagramEditor.Services.DiagramData diagram)
    {
        Navigation.NavigateTo($"/diagram-editor/{diagram.Id}");
    }

    private async Task DuplicateDiagram(global::DiagramEditor.Services.DiagramData originalDiagram)
    {
        try
        {
            var duplicatedDiagram = new global::DiagramEditor.Services.DiagramData
            {
                Id = Helpers.NanoidGenerator(),
                Name = $"{originalDiagram.Name} (Cópia)",
                Data = originalDiagram.Data,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await DiagramRepository.CreateAsync(duplicatedDiagram);
            await LoadDiagrams();
            Snackbar.Add("Diagrama duplicado com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao duplicar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteDiagram(global::DiagramEditor.Services.DiagramData diagram)
    {
        var parameters = new DialogParameters<global::DiagramEditor.Shared.ConfirmationDialog>
        {
            { x => x.ContentText, $"Tem certeza que deseja excluir o diagrama '{diagram.Name}'? Esta ação não pode ser desfeita." },
            { x => x.ButtonText, "Excluir" },
            { x => x.Color, Color.Error }
        };

        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };
        var dialog = await DialogService.ShowAsync<global::DiagramEditor.Shared.ConfirmationDialog>("Confirmar Exclusão", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                await DiagramRepository.DeleteAsync(diagram.Id);
                await LoadDiagrams();
                Snackbar.Add("Diagrama excluído com sucesso!", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao excluir diagrama: {ex.Message}", Severity.Error);
            }
        }
    }
}
