using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a link label
/// </summary>
public class LinkLabelEntity : BaseEntity
{
    [Required]
    public string LinkId { get; set; } = string.Empty;
    
    /// <summary>
    /// Label content/text
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// Position along the link (0.0 to 1.0)
    /// </summary>
    public double Distance { get; set; } = 0.5;
    
    /// <summary>
    /// Offset X from calculated position
    /// </summary>
    public double? OffsetX { get; set; }
    
    /// <summary>
    /// Offset Y from calculated position
    /// </summary>
    public double? OffsetY { get; set; }
    
    /// <summary>
    /// Whether the label is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the label is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Custom properties as JSON
    /// </summary>
    public string? PropertiesJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(LinkId))]
    public virtual LinkEntity? Link { get; set; }
}
