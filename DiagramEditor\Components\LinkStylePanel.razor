@using DiagramEditor.Models

<MudDrawer @bind-Open="IsOpen" 
           Anchor="Anchor.Left" 
           Elevation="2" 
           Variant="DrawerVariant.Temporary"
           Width="300px">
    <MudDrawerHeader>
        <MudText Typo="Typo.h6">Estilos de Links</MudText>
        <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="Close" />
    </MudDrawerHeader>
    
    <MudDrawerContainer>
        <MudContainer Class="pa-4">
            <MudStack Spacing="3">
                <!-- Tipos de Links -->
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Tipos de Links</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="2">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Primary" 
                                     FullWidth="true"
                                     OnClick="() => OnLinkTypeSelected.InvokeAsync(LinkType.Default)">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <div style="width: 40px; height: 2px; background-color: #666666;"></div>
                                    <MudText>Padrão</MudText>
                                </MudStack>
                            </MudButton>

                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Info" 
                                     FullWidth="true"
                                     OnClick="() => OnLinkTypeSelected.InvokeAsync(LinkType.DataFlow)">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <div style="width: 40px; height: 3px; background-color: #2196F3;"></div>
                                    <MudText>Fluxo de Dados</MudText>
                                </MudStack>
                            </MudButton>

                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Warning" 
                                     FullWidth="true"
                                     OnClick="() => OnLinkTypeSelected.InvokeAsync(LinkType.ControlFlow)">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <div style="width: 40px; height: 2px; background-color: #FF5722; border-style: dashed;"></div>
                                    <MudText>Controle</MudText>
                                </MudStack>
                            </MudButton>

                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary" 
                                     FullWidth="true"
                                     OnClick="() => OnLinkTypeSelected.InvokeAsync(LinkType.Dependency)">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <div style="width: 40px; height: 1px; background-color: #9C27B0; border-style: dotted;"></div>
                                    <MudText>Dependência</MudText>
                                </MudStack>
                            </MudButton>

                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Success" 
                                     FullWidth="true"
                                     OnClick="() => OnLinkTypeSelected.InvokeAsync(LinkType.Bidirectional)">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <div style="width: 40px; height: 2px; background-color: #4CAF50;"></div>
                                    <MudText>Bidirecional</MudText>
                                </MudStack>
                            </MudButton>
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Configurações do Link Selecionado -->
                @if (SelectedLink != null)
                {
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Propriedades do Link</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="2">
                                <MudTextField @bind-Value="LinkLabel" 
                                            Label="Rótulo" 
                                            Variant="Variant.Outlined"
                                            OnBlur="UpdateLinkLabel" />

                                <MudColorPicker @bind-Value="LinkColor" 
                                              Label="Cor"
                                              OnColorChanged="UpdateLinkColor" />

                                <MudSlider @bind-Value="LinkWidth"
                                         Min="1"
                                         Max="10"
                                         Step="1"
                                         ValueLabel="true"
                                         OnChange="UpdateLinkWidth">
                                    Espessura: @LinkWidth px
                                </MudSlider>

                                <MudSelect @bind-Value="LinkStyle" 
                                         Label="Estilo" 
                                         Variant="Variant.Outlined"
                                         OnSelectionChanged="UpdateLinkStyle">
                                    <MudSelectItem Value="@("solid")">Sólido</MudSelectItem>
                                    <MudSelectItem Value="@("dashed")">Tracejado</MudSelectItem>
                                    <MudSelectItem Value="@("dotted")">Pontilhado</MudSelectItem>
                                </MudSelect>

                                <MudSwitch @bind-Checked="LinkAnimated" 
                                         Label="Animado"
                                         OnChange="UpdateLinkAnimation" />
                            </MudStack>
                        </MudCardContent>
                    </MudCard>

                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Error" 
                             OnClick="DeleteLink"
                             FullWidth="true">
                        Excluir Link
                    </MudButton>
                }
                else
                {
                    <MudAlert Severity="Severity.Info">
                        Selecione um link para editar suas propriedades.
                    </MudAlert>
                }
            </MudStack>
        </MudContainer>
    </MudDrawerContainer>
</MudDrawer>

@code {
    [Parameter] public bool IsOpen { get; set; }
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }
    [Parameter] public CustomLinkModel? SelectedLink { get; set; }
    [Parameter] public EventCallback<LinkType> OnLinkTypeSelected { get; set; }
    [Parameter] public EventCallback OnLinkUpdated { get; set; }
    [Parameter] public EventCallback OnLinkDeleted { get; set; }

    private string LinkLabel = "";
    private MudBlazor.Utilities.MudColor LinkColor = "#666666";
    private int LinkWidth = 2;
    private string LinkStyle = "solid";
    private bool LinkAnimated = false;

    protected override void OnParametersSet()
    {
        if (SelectedLink != null)
        {
            LoadLinkProperties();
        }
    }

    private void LoadLinkProperties()
    {
        if (SelectedLink == null) return;

        LinkLabel = SelectedLink.Label;
        LinkColor = SelectedLink.Color;
        LinkWidth = (int)SelectedLink.Width;
        LinkStyle = SelectedLink.Style;
        LinkAnimated = SelectedLink.Animated;
    }

    private async Task Close()
    {
        IsOpen = false;
        await IsOpenChanged.InvokeAsync(IsOpen);
    }

    private void UpdateLinkLabel()
    {
        if (SelectedLink != null)
        {
            SelectedLink.Label = LinkLabel;
            OnLinkUpdated.InvokeAsync();
        }
    }

    private void UpdateLinkColor(MudBlazor.Utilities.MudColor color)
    {
        if (SelectedLink != null)
        {
            SelectedLink.Color = color.ToString();
            OnLinkUpdated.InvokeAsync();
        }
    }

    private void UpdateLinkWidth(int width)
    {
        if (SelectedLink != null)
        {
            SelectedLink.Width = width;
            OnLinkUpdated.InvokeAsync();
        }
    }

    private void UpdateLinkStyle(string style)
    {
        if (SelectedLink != null)
        {
            SelectedLink.Style = style;
            OnLinkUpdated.InvokeAsync();
        }
    }

    private void UpdateLinkAnimation(bool animated)
    {
        if (SelectedLink != null)
        {
            SelectedLink.Animated = animated;
            OnLinkUpdated.InvokeAsync();
        }
    }

    private void DeleteLink()
    {
        OnLinkDeleted.InvokeAsync();
        Close();
    }
}
