using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using System.Collections.Generic;

namespace Blazor.Diagrams.Core.Persistence.Serialization;

/// <summary>
/// Interface for serializing and deserializing diagram models
/// </summary>
public interface IModelSerializer
{
    /// <summary>
    /// Serialize a diagram to entities
    /// </summary>
    DiagramEntity SerializeDiagram(Diagram diagram, string? name = null, string? description = null);
    
    /// <summary>
    /// Deserialize entities to a diagram
    /// </summary>
    Diagram DeserializeDiagram(DiagramEntity diagramEntity, IEnumerable<NodeEntity> nodes, 
        IEnumerable<LinkEntity> links, IEnumerable<GroupEntity> groups);
    
    /// <summary>
    /// Serialize a node model to entity
    /// </summary>
    NodeEntity SerializeNode(NodeModel node, string diagramId);
    
    /// <summary>
    /// Deserialize a node entity to model
    /// </summary>
    NodeModel DeserializeNode(NodeEntity nodeEntity, IEnumerable<PortEntity> ports);
    
    /// <summary>
    /// Serialize a link model to entity
    /// </summary>
    LinkEntity SerializeLink(BaseLinkModel link, string diagramId);
    
    /// <summary>
    /// Deserialize a link entity to model
    /// </summary>
    BaseLinkModel DeserializeLink(LinkEntity linkEntity, IEnumerable<LinkVertexEntity> vertices, 
        IEnumerable<LinkLabelEntity> labels, Dictionary<string, NodeModel> nodeMap);
    
    /// <summary>
    /// Serialize a group model to entity
    /// </summary>
    GroupEntity SerializeGroup(GroupModel group, string diagramId);
    
    /// <summary>
    /// Deserialize a group entity to model
    /// </summary>
    GroupModel DeserializeGroup(GroupEntity groupEntity, IEnumerable<NodeModel> childNodes);
    
    /// <summary>
    /// Serialize a port model to entity
    /// </summary>
    PortEntity SerializePort(PortModel port, string nodeId);
    
    /// <summary>
    /// Deserialize a port entity to model
    /// </summary>
    PortModel DeserializePort(PortEntity portEntity, NodeModel parentNode);
}
