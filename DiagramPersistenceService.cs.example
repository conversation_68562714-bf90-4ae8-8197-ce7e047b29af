using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Persistence;
using DiagramEditor.Data;
using DiagramEditor.Models;
using Microsoft.EntityFrameworkCore;

namespace DiagramEditor.Services
{
    public class DiagramPersistenceService : IDiagramPersistenceService
    {
        private readonly DiagramDbContext _context;

        public DiagramPersistenceService(DiagramDbContext context)
        {
            _context = context;
        }

        public async Task<string> SaveDiagramAsync(BlazorDiagram diagram, string name, string description = null)
        {
            // Verificar se já existe um diagrama com este ID
            var existingDiagram = await _context.Diagrams
                .Include(d => d.Nodes)
                .ThenInclude(n => n.Ports)
                .Include(d => d.Links)
                .FirstOrDefaultAsync(d => d.Id == diagram.Id);

            DiagramEntity diagramEntity;

            if (existingDiagram != null)
            {
                // Atualizar diagrama existente
                diagramEntity = existingDiagram;
                diagramEntity.Name = name;
                diagramEntity.Description = description;
                diagramEntity.UpdatedAt = DateTime.Now;

                // Limpar nós e links existentes
                _context.Nodes.RemoveRange(diagramEntity.Nodes);
                _context.Links.RemoveRange(diagramEntity.Links);
            }
            else
            {
                // Criar novo diagrama
                diagramEntity = new DiagramEntity
                {
                    Id = diagram.Id ?? Guid.NewGuid().ToString(),
                    Name = name,
                    Description = description,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Diagrams.Add(diagramEntity);
            }

            // Adicionar nós
            foreach (var node in diagram.Nodes)
            {
                var nodeEntity = NodeEntity.FromModel(node, diagramEntity.Id);
                diagramEntity.Nodes.Add(nodeEntity);
            }

            // Adicionar links
            foreach (var link in diagram.Links)
            {
                var linkEntity = LinkEntity.FromModel(link, diagramEntity.Id);
                if (linkEntity != null)
                {
                    diagramEntity.Links.Add(linkEntity);
                }
            }

            await _context.SaveChangesAsync();
            return diagramEntity.Id;
        }

        public async Task<BlazorDiagram> LoadDiagramAsync(string diagramId)
        {
            var diagramEntity = await _context.Diagrams
                .Include(d => d.Nodes)
                .ThenInclude(n => n.Ports)
                .Include(d => d.Links)
                .FirstOrDefaultAsync(d => d.Id == diagramId);

            if (diagramEntity == null)
                return null;

            var diagram = new BlazorDiagram();
            diagram.Id = diagramEntity.Id;

            // Criar mapa de nós para referência posterior
            var nodesMap = new Dictionary<string, NodeModel>();

            // Adicionar nós
            foreach (var nodeEntity in diagramEntity.Nodes)
            {
                var nodeModel = nodeEntity.ToModel();
                diagram.AddNode(nodeModel);
                nodesMap[nodeModel.Id] = nodeModel;
            }

            // Adicionar links
            foreach (var linkEntity in diagramEntity.Links)
            {
                var linkModel = linkEntity.ToModel(nodesMap);
                if (linkModel != null)
                {
                    diagram.AddLink(linkModel);
                }
            }

            return diagram;
        }

        public async Task<List<DiagramEntity>> GetAllDiagramsAsync()
        {
            return await _context.Diagrams
                .OrderByDescending(d => d.UpdatedAt)
                .ToListAsync();
        }

        public async Task<bool> DeleteDiagramAsync(string diagramId)
        {
            var diagram = await _context.Diagrams.FindAsync(diagramId);
            if (diagram == null)
                return false;

            _context.Diagrams.Remove(diagram);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}