@using DiagramEditor.Models
@using Blazor.Diagrams.Core.Models
@inherits ComponentBase

<div class="circle-node" 
     style="width: @(Node.Size?.Width ?? 80)px; 
            height: @(Node.Size?.Height ?? 80)px; 
            background-color: @Node.BackgroundColor; 
            border: @(Node.BorderWidth)px solid @Node.BorderColor;
            color: @Node.TextColor;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;">
    
    @if (!string.IsNullOrEmpty(Node.Title))
    {
        <span>@Node.Title</span>
    }
</div>

<style>
    .circle-node:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .circle-node.selected {
        border-color: #FF5722 !important;
        border-width: 3px !important;
    }
</style>

@code {
    [Parameter] public CircleNodeModel Node { get; set; } = null!;
}
