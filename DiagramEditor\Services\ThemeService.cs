using MudBlazor;

namespace DiagramEditor.Services;

/// <summary>
/// Serviço para gerenciamento de temas da aplicação
/// </summary>
public interface IThemeService
{
    MudTheme CurrentTheme { get; }
    DiagramTheme CurrentDiagramTheme { get; }
    ThemeMode CurrentMode { get; }
    event Action? ThemeChanged;
    
    void SetTheme(ThemePreset preset);
    void SetMode(ThemeMode mode);
    void SetCustomTheme(MudTheme theme, DiagramTheme diagramTheme);
    Task SaveThemePreferenceAsync();
    Task LoadThemePreferenceAsync();
}

public class ThemeService : IThemeService
{
    private MudTheme _currentTheme;
    private DiagramTheme _currentDiagramTheme;
    private ThemeMode _currentMode;

    public MudTheme CurrentTheme => _currentTheme;
    public DiagramTheme CurrentDiagramTheme => _currentDiagramTheme;
    public ThemeMode CurrentMode => _currentMode;

    public event Action? ThemeChanged;

    public ThemeService()
    {
        _currentMode = ThemeMode.Light;
        _currentTheme = CreateLightTheme();
        _currentDiagramTheme = CreateLightDiagramTheme();
    }

    public void SetTheme(ThemePreset preset)
    {
        switch (preset)
        {
            case ThemePreset.Light:
                _currentMode = ThemeMode.Light;
                _currentTheme = CreateLightTheme();
                _currentDiagramTheme = CreateLightDiagramTheme();
                break;
            case ThemePreset.Dark:
                _currentMode = ThemeMode.Dark;
                _currentTheme = CreateDarkTheme();
                _currentDiagramTheme = CreateDarkDiagramTheme();
                break;
            case ThemePreset.Blue:
                _currentMode = ThemeMode.Light;
                _currentTheme = CreateBlueTheme();
                _currentDiagramTheme = CreateBlueDiagramTheme();
                break;
            case ThemePreset.Green:
                _currentMode = ThemeMode.Light;
                _currentTheme = CreateGreenTheme();
                _currentDiagramTheme = CreateGreenDiagramTheme();
                break;
            case ThemePreset.Purple:
                _currentMode = ThemeMode.Light;
                _currentTheme = CreatePurpleTheme();
                _currentDiagramTheme = CreatePurpleDiagramTheme();
                break;
            case ThemePreset.HighContrast:
                _currentMode = ThemeMode.Dark;
                _currentTheme = CreateHighContrastTheme();
                _currentDiagramTheme = CreateHighContrastDiagramTheme();
                break;
        }
        
        ThemeChanged?.Invoke();
    }

    public void SetMode(ThemeMode mode)
    {
        _currentMode = mode;
        
        // Ajustar tema baseado no modo
        if (mode == ThemeMode.Dark && !_currentTheme.Palette.Dark.Background.StartsWith("#1"))
        {
            SetTheme(ThemePreset.Dark);
        }
        else if (mode == ThemeMode.Light && _currentTheme.Palette.Dark.Background.StartsWith("#1"))
        {
            SetTheme(ThemePreset.Light);
        }
        
        ThemeChanged?.Invoke();
    }

    public void SetCustomTheme(MudTheme theme, DiagramTheme diagramTheme)
    {
        _currentTheme = theme;
        _currentDiagramTheme = diagramTheme;
        ThemeChanged?.Invoke();
    }

    public async Task SaveThemePreferenceAsync()
    {
        // Implementar salvamento em localStorage via JavaScript
        await Task.CompletedTask;
    }

    public async Task LoadThemePreferenceAsync()
    {
        // Implementar carregamento do localStorage via JavaScript
        await Task.CompletedTask;
    }

    private MudTheme CreateLightTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteLight()
            {
                Primary = "#2196F3",
                Secondary = "#FF5722",
                Success = "#4CAF50",
                Warning = "#FF9800",
                Error = "#F44336",
                Info = "#2196F3",
                Background = "#FFFFFF",
                Surface = "#F5F5F5",
                AppbarBackground = "#2196F3",
                DrawerBackground = "#FFFFFF",
                ActionDefault = "#424242",
                ActionDisabled = "#BDBDBD",
                TextPrimary = "#212121",
                TextSecondary = "#757575"
            },
            Typography = new Typography()
            {
                Default = new Default()
                {
                    FontFamily = new[] { "Roboto", "Helvetica", "Arial", "sans-serif" }
                }
            }
        };
    }

    private MudTheme CreateDarkTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteDark()
            {
                Primary = "#90CAF9",
                Secondary = "#FF8A65",
                Success = "#81C784",
                Warning = "#FFB74D",
                Error = "#E57373",
                Info = "#64B5F6",
                Background = "#121212",
                Surface = "#1E1E1E",
                AppbarBackground = "#1976D2",
                DrawerBackground = "#1E1E1E",
                ActionDefault = "#E0E0E0",
                ActionDisabled = "#616161",
                TextPrimary = "#FFFFFF",
                TextSecondary = "#AAAAAA"
            }
        };
    }

    private MudTheme CreateBlueTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteLight()
            {
                Primary = "#1976D2",
                Secondary = "#0D47A1",
                Success = "#388E3C",
                Warning = "#F57C00",
                Error = "#D32F2F",
                Info = "#1976D2",
                Background = "#F3F8FF",
                Surface = "#E3F2FD",
                AppbarBackground = "#1976D2"
            }
        };
    }

    private MudTheme CreateGreenTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteLight()
            {
                Primary = "#4CAF50",
                Secondary = "#2E7D32",
                Success = "#66BB6A",
                Warning = "#FF9800",
                Error = "#F44336",
                Info = "#2196F3",
                Background = "#F1F8E9",
                Surface = "#E8F5E8",
                AppbarBackground = "#4CAF50"
            }
        };
    }

    private MudTheme CreatePurpleTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteLight()
            {
                Primary = "#9C27B0",
                Secondary = "#673AB7",
                Success = "#4CAF50",
                Warning = "#FF9800",
                Error = "#F44336",
                Info = "#2196F3",
                Background = "#F8F5FF",
                Surface = "#F3E5F5",
                AppbarBackground = "#9C27B0"
            }
        };
    }

    private MudTheme CreateHighContrastTheme()
    {
        return new MudTheme()
        {
            Palette = new PaletteDark()
            {
                Primary = "#FFFF00",
                Secondary = "#00FFFF",
                Success = "#00FF00",
                Warning = "#FFA500",
                Error = "#FF0000",
                Info = "#00BFFF",
                Background = "#000000",
                Surface = "#1A1A1A",
                AppbarBackground = "#000000",
                TextPrimary = "#FFFFFF",
                TextSecondary = "#FFFF00"
            }
        };
    }

    private DiagramTheme CreateLightDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#FFFFFF",
            GridColor = "#E0E0E0",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#2196F3",
                BorderColor = "#1976D2",
                TextColor = "#FFFFFF",
                BorderWidth = 2
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#666666",
                Width = 2,
                SelectedColor = "#FF5722"
            }
        };
    }

    private DiagramTheme CreateDarkDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#1E1E1E",
            GridColor = "#404040",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#90CAF9",
                BorderColor = "#64B5F6",
                TextColor = "#000000",
                BorderWidth = 2
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#AAAAAA",
                Width = 2,
                SelectedColor = "#FF8A65"
            }
        };
    }

    private DiagramTheme CreateBlueDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#F3F8FF",
            GridColor = "#BBDEFB",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#1976D2",
                BorderColor = "#0D47A1",
                TextColor = "#FFFFFF",
                BorderWidth = 2
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#1976D2",
                Width = 2,
                SelectedColor = "#0D47A1"
            }
        };
    }

    private DiagramTheme CreateGreenDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#F1F8E9",
            GridColor = "#C8E6C9",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#4CAF50",
                BorderColor = "#2E7D32",
                TextColor = "#FFFFFF",
                BorderWidth = 2
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#4CAF50",
                Width = 2,
                SelectedColor = "#2E7D32"
            }
        };
    }

    private DiagramTheme CreatePurpleDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#F8F5FF",
            GridColor = "#E1BEE7",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#9C27B0",
                BorderColor = "#673AB7",
                TextColor = "#FFFFFF",
                BorderWidth = 2
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#9C27B0",
                Width = 2,
                SelectedColor = "#673AB7"
            }
        };
    }

    private DiagramTheme CreateHighContrastDiagramTheme()
    {
        return new DiagramTheme
        {
            CanvasBackground = "#000000",
            GridColor = "#FFFF00",
            NodeDefaults = new NodeTheme
            {
                BackgroundColor = "#FFFF00",
                BorderColor = "#FFFFFF",
                TextColor = "#000000",
                BorderWidth = 3
            },
            LinkDefaults = new LinkTheme
            {
                Color = "#FFFFFF",
                Width = 3,
                SelectedColor = "#00FFFF"
            }
        };
    }
}

/// <summary>
/// Enumeração dos presets de temas disponíveis
/// </summary>
public enum ThemePreset
{
    Light,
    Dark,
    Blue,
    Green,
    Purple,
    HighContrast
}

/// <summary>
/// Modo do tema (claro ou escuro)
/// </summary>
public enum ThemeMode
{
    Light,
    Dark
}

/// <summary>
/// Tema específico para diagramas
/// </summary>
public class DiagramTheme
{
    public string CanvasBackground { get; set; } = "#FFFFFF";
    public string GridColor { get; set; } = "#E0E0E0";
    public NodeTheme NodeDefaults { get; set; } = new();
    public LinkTheme LinkDefaults { get; set; } = new();
}

/// <summary>
/// Tema para nós do diagrama
/// </summary>
public class NodeTheme
{
    public string BackgroundColor { get; set; } = "#2196F3";
    public string BorderColor { get; set; } = "#1976D2";
    public string TextColor { get; set; } = "#FFFFFF";
    public int BorderWidth { get; set; } = 2;
}

/// <summary>
/// Tema para links do diagrama
/// </summary>
public class LinkTheme
{
    public string Color { get; set; } = "#666666";
    public int Width { get; set; } = 2;
    public string SelectedColor { get; set; } = "#FF5722";
}
