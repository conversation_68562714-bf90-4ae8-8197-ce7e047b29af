@using DiagramEditor.Services
@using DiagramEditor.Hu<PERSON>
@inject ICollaborationService CollaborationService
@inject ISnackbar Snackbar

<MudDrawer @bind-Open="IsOpen" 
           Anchor="Anchor.Right" 
           Elevation="2" 
           Variant="DrawerVariant.Temporary"
           Width="350px">
    <MudDrawerHeader>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.People" />
            <MudText Typo="Typo.h6">Colaboração</MudText>
            <MudSpacer />
            <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="Close" Size="MudBlazor.Size.Small" />
        </MudStack>
    </MudDrawerHeader>
    
    <MudDrawerContainer>
        <MudContainer Class="pa-4">
            <MudStack Spacing="3">
                <!-- Status da Conexão -->
                <MudCard Elevation="1">
                    <MudCardContent>
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@GetConnectionIcon()" 
                                   Color="@GetConnectionColor()" />
                            <MudText Typo="Typo.body1">
                                @GetConnectionStatus()
                            </MudText>
                            @if (!CollaborationService.IsConnected)
                            {
                                <MudSpacer />
                                <MudButton Size="MudBlazor.Size.Small"
                                         Variant="Variant.Outlined"
                                         Color="Color.Primary"
                                         OnClick="ConnectAsync">
                                    Conectar
                                </MudButton>
                            }
                        </MudStack>
                    </MudCardContent>
                </MudCard>

                <!-- Configurações do Usuário -->
                @if (CollaborationService.IsConnected)
                {
                    <MudCard Elevation="1">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Seu Perfil</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="2">
                                <MudTextField @bind-Value="UserName" 
                                            Label="Nome de usuário" 
                                            Variant="Variant.Outlined"
                                            Disabled="@(!string.IsNullOrEmpty(CollaborationService.CurrentDiagramId))" />
                                
                                <MudColorPicker @bind-Value="UserColor" 
                                              Label="Sua cor"
                                              Disabled="@(!string.IsNullOrEmpty(CollaborationService.CurrentDiagramId))" />
                                
                                @if (string.IsNullOrEmpty(CollaborationService.CurrentDiagramId))
                                {
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary" 
                                             OnClick="JoinCurrentDiagramAsync"
                                             FullWidth="true"
                                             StartIcon="@Icons.Material.Filled.Login">
                                        Entrar na Sessão
                                    </MudButton>
                                }
                                else
                                {
                                    <MudButton Variant="Variant.Outlined" 
                                             Color="Color.Error" 
                                             OnClick="LeaveDiagramAsync"
                                             FullWidth="true"
                                             StartIcon="@Icons.Material.Filled.Logout">
                                        Sair da Sessão
                                    </MudButton>
                                }
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                }

                <!-- Usuários Conectados -->
                @if (CollaborationService.IsConnected && !string.IsNullOrEmpty(CollaborationService.CurrentDiagramId))
                {
                    <MudCard Elevation="1">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudText Typo="Typo.h6">Usuários Online</MudText>
                                    <MudChip Size="MudBlazor.Size.Small" Color="Color.Primary">
                                        @ConnectedUsers.Count
                                    </MudChip>
                                </MudStack>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            @if (ConnectedUsers.Any())
                            {
                                <MudStack Spacing="1">
                                    @foreach (var user in ConnectedUsers)
                                    {
                                        <MudPaper Class="pa-2" Elevation="0" Style="@($"border-left: 4px solid {user.Color};")">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudAvatar Size="MudBlazor.Size.Small" 
                                                         Style="@($"background-color: {user.Color}; color: white;")">
                                                    @GetUserInitials(user.UserName)
                                                </MudAvatar>
                                                <MudStack Spacing="0">
                                                    <MudText Typo="Typo.body2">@user.UserName</MudText>
                                                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                                        Online há @GetTimeOnline(user.JoinedAt)
                                                    </MudText>
                                                </MudStack>
                                                <MudSpacer />
                                                <MudIcon Icon="@Icons.Material.Filled.Circle" 
                                                       Size="MudBlazor.Size.Small" 
                                                       Color="Color.Success" />
                                            </MudStack>
                                        </MudPaper>
                                    }
                                </MudStack>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Info" Dense="true">
                                    Nenhum outro usuário conectado
                                </MudAlert>
                            }
                        </MudCardContent>
                    </MudCard>
                }

                <!-- Cursores de Outros Usuários -->
                @if (CursorPositions.Any())
                {
                    <MudCard Elevation="1">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Cursores Ativos</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="1">
                                @foreach (var cursor in CursorPositions.Values)
                                {
                                    <MudChip Size="MudBlazor.Size.Small" 
                                           Style="@($"background-color: {cursor.Color}; color: white;")">
                                        @cursor.UserName: (@cursor.X.ToString("F0"), @cursor.Y.ToString("F0"))
                                    </MudChip>
                                }
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                }

                <!-- Elementos Bloqueados -->
                @if (LockedElements.Any())
                {
                    <MudCard Elevation="1">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">Elementos Bloqueados</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="1">
                                @foreach (var lockInfo in LockedElements.Values)
                                {
                                    <MudAlert Severity="Severity.Warning" Dense="true">
                                        <strong>@lockInfo.ElementId</strong> bloqueado por <strong>@lockInfo.UserName</strong>
                                    </MudAlert>
                                }
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                }

                <!-- Configurações de Colaboração -->
                <MudCard Elevation="1">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Configurações</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="2">
                            <MudSwitch @bind-Value="ShowCursors" 
                                     Label="Mostrar cursores de outros usuários" />
                            
                            <MudSwitch @bind-Value="ShowSelections" 
                                     Label="Mostrar seleções de outros usuários" />
                            
                            <MudSwitch @bind-Value="AutoLockElements" 
                                     Label="Bloquear elementos automaticamente" />
                            
                            <MudSwitch @bind-Value="PlaySounds" 
                                     Label="Sons de notificação" />
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudStack>
        </MudContainer>
    </MudDrawerContainer>
</MudDrawer>

@code {
    [Parameter] public bool IsOpen { get; set; }
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }
    [Parameter] public string? CurrentDiagramId { get; set; }

    private string UserName = $"Usuário_{Random.Shared.Next(1000, 9999)}";
    private MudBlazor.Utilities.MudColor UserColor = "#2196F3";
    private List<UserInfo> ConnectedUsers = new();
    private Dictionary<string, CursorPosition> CursorPositions = new();
    private Dictionary<string, ElementLock> LockedElements = new();

    // Configurações
    private bool ShowCursors = true;
    private bool ShowSelections = true;
    private bool AutoLockElements = false;
    private bool PlaySounds = true;

    protected override void OnInitialized()
    {
        CollaborationService.UsersUpdated += OnUsersUpdated;
        CollaborationService.UserJoined += OnUserJoined;
        CollaborationService.UserLeft += OnUserLeft;
        CollaborationService.CursorPositionReceived += OnCursorPositionReceived;
        CollaborationService.ElementLocked += OnElementLocked;
        CollaborationService.ElementUnlocked += OnElementUnlocked;
    }

    private async Task Close()
    {
        IsOpen = false;
        await IsOpenChanged.InvokeAsync(IsOpen);
    }

    private async Task ConnectAsync()
    {
        try
        {
            await CollaborationService.StartAsync();
            Snackbar.Add("Conectado ao servidor de colaboração!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao conectar: {ex.Message}", Severity.Error);
        }
    }

    private async Task JoinCurrentDiagramAsync()
    {
        if (string.IsNullOrEmpty(CurrentDiagramId))
        {
            Snackbar.Add("Nenhum diagrama selecionado", Severity.Warning);
            return;
        }

        try
        {
            await CollaborationService.JoinDiagramAsync(CurrentDiagramId, UserName, UserColor.ToString());
            Snackbar.Add($"Entrou na sessão colaborativa!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao entrar na sessão: {ex.Message}", Severity.Error);
        }
    }

    private async Task LeaveDiagramAsync()
    {
        try
        {
            await CollaborationService.LeaveDiagramAsync();
            Snackbar.Add("Saiu da sessão colaborativa", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao sair da sessão: {ex.Message}", Severity.Error);
        }
    }

    private string GetConnectionIcon()
    {
        return CollaborationService.IsConnected ? Icons.Material.Filled.Wifi : Icons.Material.Filled.WifiOff;
    }

    private Color GetConnectionColor()
    {
        return CollaborationService.IsConnected ? Color.Success : Color.Error;
    }

    private string GetConnectionStatus()
    {
        return CollaborationService.IsConnected ? "Conectado" : "Desconectado";
    }

    private string GetUserInitials(string userName)
    {
        if (string.IsNullOrEmpty(userName)) return "?";
        var parts = userName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        return userName.Substring(0, Math.Min(2, userName.Length)).ToUpper();
    }

    private string GetTimeOnline(DateTime joinedAt)
    {
        var timeSpan = DateTime.UtcNow - joinedAt;
        if (timeSpan.TotalMinutes < 1)
            return "agora";
        if (timeSpan.TotalHours < 1)
            return $"{(int)timeSpan.TotalMinutes}m";
        return $"{(int)timeSpan.TotalHours}h {(int)timeSpan.Minutes}m";
    }

    private void OnUsersUpdated(List<UserInfo> users)
    {
        ConnectedUsers = users;
        InvokeAsync(StateHasChanged);
    }

    private void OnUserJoined(UserInfo user)
    {
        if (PlaySounds)
        {
            // Tocar som de entrada
        }
        Snackbar.Add($"{user.UserName} entrou na sessão", Severity.Info);
    }

    private void OnUserLeft(UserInfo user)
    {
        if (PlaySounds)
        {
            // Tocar som de saída
        }
        Snackbar.Add($"{user.UserName} saiu da sessão", Severity.Info);
    }

    private void OnCursorPositionReceived(CursorPosition position)
    {
        if (ShowCursors)
        {
            CursorPositions[position.UserId] = position;
            InvokeAsync(StateHasChanged);
        }
    }

    private void OnElementLocked(ElementLock lockInfo)
    {
        LockedElements[lockInfo.ElementId] = lockInfo;
        InvokeAsync(StateHasChanged);
    }

    private void OnElementUnlocked(string elementId, ElementLock lockInfo)
    {
        LockedElements.Remove(elementId);
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        CollaborationService.UsersUpdated -= OnUsersUpdated;
        CollaborationService.UserJoined -= OnUserJoined;
        CollaborationService.UserLeft -= OnUserLeft;
        CollaborationService.CursorPositionReceived -= OnCursorPositionReceived;
        CollaborationService.ElementLocked -= OnElementLocked;
        CollaborationService.ElementUnlocked -= OnElementUnlocked;
    }
}
