{"version": 2, "dgSpecHash": "zL6mzUqzijs=", "success": true, "projectFilePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\nanoid\\1.0.0\\nanoid.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\svgpathproperties\\1.0.0\\svgpathproperties.1.0.0.nupkg.sha512"], "logs": [{"code": "NU1604", "level": "Warning", "message": "Project dependency Nanoid does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "libraryId": "Nanoid", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}, {"code": "NU1604", "level": "Warning", "message": "Project dependency SvgPathProperties does not contain an inclusive lower bound. Include a lower bound in the dependency version to ensure consistent restore results.", "projectPath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "warningLevel": 1, "filePath": "D:\\projects\\MudBlazor\\BOS.Blazor.Diagrams\\Blazor.Diagrams.Core\\Blazor.Diagrams.Core.csproj", "libraryId": "SvgPathProperties", "targetGraphs": [".NETCoreApp,Version=v9.0", ".NETCoreApp,Version=v8.0", ".NETCoreApp,Version=v7.0", ".NETCoreApp,Version=v6.0"]}]}