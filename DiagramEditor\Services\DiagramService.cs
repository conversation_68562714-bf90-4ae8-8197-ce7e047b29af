using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Anchors;
using System.Text.Json;

namespace DiagramEditor.Services;

public class DiagramService
{
    public string SerializeDiagram(Diagram diagram)
    {
        var data = new
        {
            Nodes = diagram.Nodes.Select(n => new
            {
                Id = n.Id,
                Title = n.Title,
                Position = new { X = n.Position.X, Y = n.Position.Y },
                Size = n.Size != null ? new { Width = n.Size.Width, Height = n.Size.Height } : null,
                Ports = n.Ports.Select(p => new
                {
                    Id = p.Id,
                    Alignment = p.Alignment.ToString()
                })
            }),
            Links = diagram.Links.Select(l => new
            {
                Id = l.Id,
                SourceNodeId = l.Source.Model?.Id,
                SourcePortId = l.Source is PortAnchor sourcePort ? sourcePort.Port.Id : null,
                TargetNodeId = l.Target.Model?.Id,
                TargetPortId = l.Target is PortAnchor targetPort ? targetPort.Port.Id : null
            })
        };

        return JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
    }

    public void DeserializeDiagram(Diagram diagram, string jsonData)
    {
        try
        {
            diagram.Nodes.Clear();
            diagram.Links.Clear();

            using var document = JsonDocument.Parse(jsonData);
            var root = document.RootElement;

            var nodeDict = new Dictionary<string, NodeModel>();

            // Deserializar nós
            if (root.TryGetProperty("Nodes", out var nodesElement))
            {
                foreach (var nodeElement in nodesElement.EnumerateArray())
                {
                    var id = nodeElement.GetProperty("Id").GetString()!;
                    var title = nodeElement.GetProperty("Title").GetString() ?? "";
                    var position = nodeElement.GetProperty("Position");
                    var x = position.GetProperty("X").GetDouble();
                    var y = position.GetProperty("Y").GetDouble();

                    var node = new NodeModel(new Point(x, y))
                    {
                        Id = id,
                        Title = title
                    };

                    // Deserializar portas
                    if (nodeElement.TryGetProperty("Ports", out var portsElement))
                    {
                        foreach (var portElement in portsElement.EnumerateArray())
                        {
                            var portId = portElement.GetProperty("Id").GetString()!;
                            var alignmentStr = portElement.GetProperty("Alignment").GetString()!;
                            
                            if (Enum.TryParse<PortAlignment>(alignmentStr, out var alignment))
                            {
                                var port = node.AddPort(alignment);
                                port.Id = portId;
                            }
                        }
                    }

                    nodeDict[id] = node;
                    diagram.Nodes.Add(node);
                }
            }

            // Deserializar links
            if (root.TryGetProperty("Links", out var linksElement))
            {
                foreach (var linkElement in linksElement.EnumerateArray())
                {
                    var linkId = linkElement.GetProperty("Id").GetString()!;
                    var sourceNodeId = linkElement.GetProperty("SourceNodeId").GetString();
                    var sourcePortId = linkElement.GetProperty("SourcePortId").GetString();
                    var targetNodeId = linkElement.GetProperty("TargetNodeId").GetString();
                    var targetPortId = linkElement.GetProperty("TargetPortId").GetString();

                    if (sourceNodeId != null && targetNodeId != null &&
                        nodeDict.TryGetValue(sourceNodeId, out var sourceNode) &&
                        nodeDict.TryGetValue(targetNodeId, out var targetNode))
                    {
                        Anchor sourceAnchor;
                        Anchor targetAnchor;

                        // Encontrar porta de origem
                        if (sourcePortId != null)
                        {
                            var sourcePort = sourceNode.Ports.FirstOrDefault(p => p.Id == sourcePortId);
                            sourceAnchor = sourcePort != null ? new PortAnchor(sourcePort) : new NodeAnchor(sourceNode);
                        }
                        else
                        {
                            sourceAnchor = new NodeAnchor(sourceNode);
                        }

                        // Encontrar porta de destino
                        if (targetPortId != null)
                        {
                            var targetPort = targetNode.Ports.FirstOrDefault(p => p.Id == targetPortId);
                            targetAnchor = targetPort != null ? new PortAnchor(targetPort) : new NodeAnchor(targetNode);
                        }
                        else
                        {
                            targetAnchor = new NodeAnchor(targetNode);
                        }

                        var link = new LinkModel(sourceAnchor, targetAnchor)
                        {
                            Id = linkId
                        };

                        diagram.Links.Add(link);
                    }
                }
            }

            diagram.Refresh();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Erro ao deserializar diagrama: {ex.Message}", ex);
        }
    }
}
