using Blazor.Diagrams.Core;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Anchors;
using Blazor.Diagrams.Core.Persistence;

using System.Text.Json;

namespace DiagramEditor.Services;

/// <summary>
/// Simplified diagram data model for UI
/// </summary>
public class DiagramData
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Data { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Simplified diagram repository interface
/// </summary>
public interface IDiagramRepository
{
    Task<DiagramData?> GetByIdAsync(string id);
    Task<IEnumerable<DiagramData>> GetAllAsync();
    Task CreateAsync(DiagramData diagram);
    Task UpdateAsync(DiagramData diagram);
    Task DeleteAsync(string id);
}

/// <summary>
/// Implementation of simplified diagram repository using Entity Framework
/// </summary>
public class DiagramRepository : IDiagramRepository
{
    private readonly IUnitOfWork _unitOfWork;

    public DiagramRepository(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<DiagramData?> GetByIdAsync(string id)
    {
        var entity = await _unitOfWork.Diagrams.GetByIdAsync(id);
        return entity == null ? null : MapToData(entity);
    }

    public async Task<IEnumerable<DiagramData>> GetAllAsync()
    {
        var entities = await _unitOfWork.Diagrams.GetAllAsync();
        return entities.Select(MapToData);
    }

    public async Task CreateAsync(DiagramData diagram)
    {
        var entity = MapToEntity(diagram);
        await _unitOfWork.Diagrams.AddAsync(entity);
        await _unitOfWork.SaveChangesAsync();
    }

    public async Task UpdateAsync(DiagramData diagram)
    {
        var entity = MapToEntity(diagram);
        await _unitOfWork.Diagrams.UpdateAsync(entity);
        await _unitOfWork.SaveChangesAsync();
    }

    public async Task DeleteAsync(string id)
    {
        await _unitOfWork.Diagrams.DeleteAsync(id);
        await _unitOfWork.SaveChangesAsync();
    }

    private static DiagramData MapToData(DiagramEntity entity)
    {
        return new DiagramData
        {
            Id = entity.Id,
            Name = entity.Name,
            Data = entity.OptionsJson ?? "{}",
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt
        };
    }

    private static DiagramEntity MapToEntity(DiagramData data)
    {
        return new DiagramEntity
        {
            Id = data.Id,
            Name = data.Name,
            OptionsJson = data.Data,
            CreatedAt = data.CreatedAt,
            UpdatedAt = data.UpdatedAt
        };
    }
}

public class DiagramService
{
    public string SerializeDiagram(Diagram diagram)
    {
        var data = new
        {
            Nodes = diagram.Nodes.Select(n => new
            {
                Id = n.Id,
                Title = n.Title,
                Position = new { X = n.Position.X, Y = n.Position.Y },
                Size = n.Size != null ? new { Width = n.Size.Width, Height = n.Size.Height } : null,
                Ports = n.Ports.Select(p => new
                {
                    Id = p.Id,
                    Alignment = p.Alignment.ToString()
                })
            }),
            Links = diagram.Links.Select(l => new
            {
                Id = l.Id,
                SourceNodeId = GetModelId(l.Source),
                SourcePortId = GetPortId(l.Source),
                TargetNodeId = GetModelId(l.Target),
                TargetPortId = GetPortId(l.Target)
            })
        };

        return JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
    }

    private string? GetModelId(Anchor anchor)
    {
        try
        {
            // Usar reflexão para acessar a propriedade Id
            var model = anchor.Model;
            if (model != null)
            {
                var idProperty = model.GetType().GetProperty("Id");
                return idProperty?.GetValue(model)?.ToString();
            }
            return null;
        }
        catch
        {
            return null;
        }
    }

    private string? GetPortId(Anchor anchor)
    {
        try
        {
            // Usar reflexão para acessar a propriedade Port se existir
            var portProperty = anchor.GetType().GetProperty("Port");
            if (portProperty != null)
            {
                var port = portProperty.GetValue(anchor);
                var idProperty = port?.GetType().GetProperty("Id");
                return idProperty?.GetValue(port)?.ToString();
            }
            return null;
        }
        catch
        {
            return null;
        }
    }

    public void DeserializeDiagram(Diagram diagram, string jsonData)
    {
        try
        {
            diagram.Nodes.Clear();
            diagram.Links.Clear();

            using var document = JsonDocument.Parse(jsonData);
            var root = document.RootElement;

            var nodeDict = new Dictionary<string, NodeModel>();

            // Deserializar nós
            if (root.TryGetProperty("Nodes", out var nodesElement))
            {
                foreach (var nodeElement in nodesElement.EnumerateArray())
                {
                    var id = nodeElement.GetProperty("Id").GetString()!;
                    var title = nodeElement.GetProperty("Title").GetString() ?? "";
                    var position = nodeElement.GetProperty("Position");
                    var x = position.GetProperty("X").GetDouble();
                    var y = position.GetProperty("Y").GetDouble();

                    var node = new NodeModel(new Point(x, y))
                    {
                        Title = title
                    };

                    // Deserializar portas
                    if (nodeElement.TryGetProperty("Ports", out var portsElement))
                    {
                        foreach (var portElement in portsElement.EnumerateArray())
                        {
                            var alignmentStr = portElement.GetProperty("Alignment").GetString()!;

                            if (Enum.TryParse<PortAlignment>(alignmentStr, out var alignment))
                            {
                                node.AddPort(alignment);
                            }
                        }
                    }

                    nodeDict[id] = node;
                    diagram.Nodes.Add(node);
                }
            }

            // Deserializar links
            if (root.TryGetProperty("Links", out var linksElement))
            {
                foreach (var linkElement in linksElement.EnumerateArray())
                {
                    var sourceNodeId = linkElement.GetProperty("SourceNodeId").GetString();
                    var sourcePortId = linkElement.GetProperty("SourcePortId").GetString();
                    var targetNodeId = linkElement.GetProperty("TargetNodeId").GetString();
                    var targetPortId = linkElement.GetProperty("TargetPortId").GetString();

                    if (sourceNodeId != null && targetNodeId != null &&
                        nodeDict.TryGetValue(sourceNodeId, out var sourceNode) &&
                        nodeDict.TryGetValue(targetNodeId, out var targetNode))
                    {
                        // Criar link simples entre nós (sem portas específicas por enquanto)
                        var link = new LinkModel(sourceNode, targetNode);
                        diagram.Links.Add(link);
                    }
                }
            }

            diagram.Refresh();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Erro ao deserializar diagrama: {ex.Message}", ex);
        }
    }
}
