@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Geometry
@using DiagramEditor.Services
@inject IResponsiveService ResponsiveService

<MudPaper Elevation="2" Class="@GetToolbarClass()" Style="@GetToolbarStyle()">
    <MudStack Row Spacing="@GetSpacing()" AlignItems="MudBlazor.AlignItems.Center">
        <!-- Informações do Diagrama -->
        @if (!ResponsiveService.IsMobile)
        {
            <MudTextField @bind-Value="DiagramName"
                         Label="Nome do Diagrama"
                         Variant="Variant.Outlined"
                         Style="@GetDiagramNameStyle()" />
        }
        
        <!-- Ações de Arquivo -->
        <MudButtonGroup Variant="Variant.Filled" Color="Color.Primary">
            <MudButton StartIcon="@Icons.Material.Filled.Save" OnClick="OnSave">
                <PERSON>var
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.FolderOpen" OnClick="OnLoad">
                Carregar
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Clear" OnClick="OnClear" Color="Color.Error">
                Limpar
            </MudButton>
        </MudButtonGroup>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Ferramentas de Nós -->
        <MudButtonGroup Variant="Variant.Outlined" Color="Color.Secondary">
            <MudButton StartIcon="@Icons.Material.Filled.CropSquare" OnClick="() => OnAddNode.InvokeAsync(NodeType.Rectangle)">
                Retângulo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Circle" OnClick="() => OnAddNode.InvokeAsync(NodeType.Circle)">
                Círculo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.ChangeHistory" OnClick="() => OnAddNode.InvokeAsync(NodeType.Triangle)">
                Triângulo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Diamond" OnClick="() => OnAddNode.InvokeAsync(NodeType.Diamond)">
                Losango
            </MudButton>
        </MudButtonGroup>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Controles de Zoom -->
        <MudStack Row Spacing="1" AlignItems="MudBlazor.AlignItems.Center">
            <MudIconButton Icon="@Icons.Material.Filled.ZoomOut" OnClick="OnZoomOut" Size="MudBlazor.Size.Small" />
            <MudText Typo="Typo.body2" Style="min-width: 60px; text-align: center;">
                @($"{ZoomLevel:P0}")
            </MudText>
            <MudIconButton Icon="@Icons.Material.Filled.ZoomIn" OnClick="OnZoomIn" Size="MudBlazor.Size.Small" />
            <MudIconButton Icon="@Icons.Material.Filled.CenterFocusStrong" OnClick="OnResetView" Size="MudBlazor.Size.Small" />
        </MudStack>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Ferramentas de Seleção -->
        <MudButtonGroup Variant="Variant.Text" Color="Color.Info">
            <MudButton StartIcon="@Icons.Material.Filled.SelectAll" OnClick="OnSelectAll">
                Selecionar Tudo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Delete" OnClick="OnDeleteSelected" Color="Color.Error">
                Excluir Selecionados
            </MudButton>
        </MudButtonGroup>

        <MudSpacer />

        <!-- Exportação -->
        <MudButtonGroup Variant="Variant.Outlined" Color="Color.Success">
            <MudButton StartIcon="@Icons.Material.Filled.Image" OnClick="OnExportImage">
                PNG
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.PictureAsPdf" OnClick="OnExportPdf">
                PDF
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Code" OnClick="OnExportJson">
                JSON
            </MudButton>
        </MudButtonGroup>
    </MudStack>
</MudPaper>

@code {
    [Parameter] public string DiagramName { get; set; } = "Novo Diagrama";
    [Parameter] public EventCallback<string> DiagramNameChanged { get; set; }
    [Parameter] public double ZoomLevel { get; set; } = 1.0;
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnLoad { get; set; }
    [Parameter] public EventCallback OnClear { get; set; }
    [Parameter] public EventCallback<NodeType> OnAddNode { get; set; }
    [Parameter] public EventCallback OnZoomIn { get; set; }
    [Parameter] public EventCallback OnZoomOut { get; set; }
    [Parameter] public EventCallback OnResetView { get; set; }
    [Parameter] public EventCallback OnSelectAll { get; set; }
    [Parameter] public EventCallback OnDeleteSelected { get; set; }
    [Parameter] public EventCallback OnExportImage { get; set; }
    [Parameter] public EventCallback OnExportPdf { get; set; }
    [Parameter] public EventCallback OnExportJson { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await ResponsiveService.InitializeAsync();
        ResponsiveService.DeviceInfoChanged += OnDeviceInfoChanged;
    }

    private void OnDeviceInfoChanged(DeviceInfo deviceInfo)
    {
        InvokeAsync(StateHasChanged);
    }

    private string GetToolbarClass()
    {
        var baseClass = "pa-2 mb-2 diagram-toolbar";
        return ResponsiveService.IsMobile ? $"{baseClass} mobile-toolbar" :
               ResponsiveService.IsTablet ? $"{baseClass} tablet-toolbar" :
               $"{baseClass} desktop-toolbar";
    }

    private string GetToolbarStyle()
    {
        var height = ResponsiveService.IsMobile ? "auto" : "64px";
        return $"min-height: {height};";
    }

    private int GetSpacing()
    {
        return ResponsiveService.IsMobile ? 1 :
               ResponsiveService.IsTablet ? 2 : 3;
    }



    private string GetDiagramNameStyle()
    {
        var minWidth = ResponsiveService.IsMobile ? "150px" :
                      ResponsiveService.IsTablet ? "180px" : "200px";
        return $"min-width: {minWidth};";
    }

    public void Dispose()
    {
        ResponsiveService.DeviceInfoChanged -= OnDeviceInfoChanged;
    }

    public enum NodeType
    {
        Rectangle,
        Circle,
        Triangle,
        Diamond
    }
}
