@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Geometry

<MudPaper Elevation="2" Class="pa-2 mb-2">
    <MudStack Row Spacing="2" AlignItems="MudBlazor.AlignItems.Center" Wrap="Wrap.Wrap">
        <!-- Informações do Diagrama -->
        <MudTextField @bind-Value="DiagramName" 
                     Label="Nome do Diagrama" 
                     Variant="Variant.Outlined" 
                     Style="min-width: 200px;" />
        
        <!-- Ações de Arquivo -->
        <MudButtonGroup Variant="Variant.Filled" Color="Color.Primary">
            <MudButton StartIcon="@Icons.Material.Filled.Save" OnClick="OnSave">
                Salvar
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.FolderOpen" OnClick="OnLoad">
                Carregar
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Clear" OnClick="OnClear" Color="Color.Error">
                Limpar
            </MudButton>
        </MudButtonGroup>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Ferramentas de Nós -->
        <MudButtonGroup Variant="Variant.Outlined" Color="Color.Secondary">
            <MudButton StartIcon="@Icons.Material.Filled.CropSquare" OnClick="() => OnAddNode(NodeType.Rectangle)">
                Retângulo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Circle" OnClick="() => OnAddNode(NodeType.Circle)">
                Círculo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.ChangeHistory" OnClick="() => OnAddNode(NodeType.Triangle)">
                Triângulo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Diamond" OnClick="() => OnAddNode(NodeType.Diamond)">
                Losango
            </MudButton>
        </MudButtonGroup>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Controles de Zoom -->
        <MudStack Row Spacing="1" AlignItems="MudBlazor.AlignItems.Center">
            <MudIconButton Icon="@Icons.Material.Filled.ZoomOut" OnClick="OnZoomOut" Size="MudBlazor.Size.Small" />
            <MudText Typo="Typo.body2" Style="min-width: 60px; text-align: center;">
                @($"{ZoomLevel:P0}")
            </MudText>
            <MudIconButton Icon="@Icons.Material.Filled.ZoomIn" OnClick="OnZoomIn" Size="MudBlazor.Size.Small" />
            <MudIconButton Icon="@Icons.Material.Filled.CenterFocusStrong" OnClick="OnResetView" Size="MudBlazor.Size.Small" />
        </MudStack>

        <MudDivider Vertical="true" FlexItem="true" />

        <!-- Ferramentas de Seleção -->
        <MudButtonGroup Variant="Variant.Text" Color="Color.Info">
            <MudButton StartIcon="@Icons.Material.Filled.SelectAll" OnClick="OnSelectAll">
                Selecionar Tudo
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Delete" OnClick="OnDeleteSelected" Color="Color.Error">
                Excluir Selecionados
            </MudButton>
        </MudButtonGroup>

        <MudSpacer />

        <!-- Exportação -->
        <MudButtonGroup Variant="Variant.Outlined" Color="Color.Success">
            <MudButton StartIcon="@Icons.Material.Filled.Image" OnClick="OnExportImage">
                PNG
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.PictureAsPdf" OnClick="OnExportPdf">
                PDF
            </MudButton>
            <MudButton StartIcon="@Icons.Material.Filled.Code" OnClick="OnExportJson">
                JSON
            </MudButton>
        </MudButtonGroup>
    </MudStack>
</MudPaper>

@code {
    [Parameter] public string DiagramName { get; set; } = "Novo Diagrama";
    [Parameter] public EventCallback<string> DiagramNameChanged { get; set; }
    [Parameter] public double ZoomLevel { get; set; } = 1.0;
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnLoad { get; set; }
    [Parameter] public EventCallback OnClear { get; set; }
    [Parameter] public EventCallback<NodeType> OnAddNode { get; set; }
    [Parameter] public EventCallback OnZoomIn { get; set; }
    [Parameter] public EventCallback OnZoomOut { get; set; }
    [Parameter] public EventCallback OnResetView { get; set; }
    [Parameter] public EventCallback OnSelectAll { get; set; }
    [Parameter] public EventCallback OnDeleteSelected { get; set; }
    [Parameter] public EventCallback OnExportImage { get; set; }
    [Parameter] public EventCallback OnExportPdf { get; set; }
    [Parameter] public EventCallback OnExportJson { get; set; }

    public enum NodeType
    {
        Rectangle,
        Circle,
        Triangle,
        Diamond
    }
}
