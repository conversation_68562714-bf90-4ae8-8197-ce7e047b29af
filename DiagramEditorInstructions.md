# Instruções para Configuração do Projeto DiagramEditor

## 1. Atualizar _Imports.razor

Adicione as seguintes linhas ao arquivo `_Imports.razor`:

```razor
@using MudBlazor
@using Blazor.Diagrams
@using Blazor.Diagrams.Core
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Models.Base
@using Blazor.Diagrams.Core.Geometry
@using Blazor.Diagrams.Components
@using Blazor.Diagrams.Options
```

## 2. Atualizar Program.cs

Adicione as seguintes linhas ao arquivo `Program.cs` antes de `builder.Services.AddRazorPages();`:

```csharp
using MudBlazor.Services;
using Blazor.Diagrams.Core.Persistence;
using Blazor.Diagrams.Persistence.Services;

// Adicionar serviços MudBlazor
builder.Services.AddMudServices();

// Adicionar serviços de persistência de diagramas
builder.Services.AddScoped<IDiagramPersistenceService, DiagramPersistenceService>();
```

## 3. Atualizar MainLayout.razor

Substitua o conteúdo do arquivo `Shared/MainLayout.razor` por:

```razor
@inherits LayoutComponentBase

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">Editor de Diagramas</MudText>
        <MudSpacer />
    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="my-4 pt-4">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
}
```

## 4. Atualizar _Host.cshtml

Adicione as seguintes linhas na seção `<head>` do arquivo `Pages/_Host.cshtml`:

```html
<link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
<link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
<link href="_content/Z.Blazor.Diagrams/style.min.css" rel="stylesheet" />
<link href="_content/Z.Blazor.Diagrams/default.styles.min.css" rel="stylesheet" />
```

E adicione este script antes do fechamento da tag `</body>`:

```html
<script src="_content/MudBlazor/MudBlazor.min.js"></script>
<script src="_content/Z.Blazor.Diagrams/script.min.js"></script>
```

## 5. Criar Componentes para o Editor de Diagramas

### 5.1. Criar DiagramEditorPage.razor

Crie um novo arquivo `Pages/DiagramEditorPage.razor` com o seguinte conteúdo:

```razor
@page "/diagram-editor"
@using Blazor.Diagrams.Core.PathGenerators
@using Blazor.Diagrams.Core.Routers
@using Blazor.Diagrams.Core.Persistence
@inject IDiagramPersistenceService DiagramPersistenceService

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="d-flex flex-column" Style="height: calc(100vh - 150px);">
    <MudPaper Elevation="3" Class="pa-3 mb-3">
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="AddNode" StartIcon="@Icons.Material.Filled.Add" Class="mr-2">Adicionar Nó</MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="string" Label="Tipo de Link" Value="_selectedLinkType" ValueChanged="OnLinkTypeChanged">
                    <MudSelectItem Value="@("straight")">Reto</MudSelectItem>
                    <MudSelectItem Value="@("smooth")">Suave</MudSelectItem>
                    <MudSelectItem Value="@("orthogonal")">Ortogonal</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="SaveDiagram" StartIcon="@Icons.Material.Filled.Save" Class="mr-2">Salvar</MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Info" OnClick="LoadDiagram" StartIcon="@Icons.Material.Filled.Upload">Carregar</MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <MudPaper Elevation="3" Class="flex-grow-1 position-relative">
        <DiagramCanvas @ref="_diagramCanvas" BlazorDiagram="_blazorDiagram" Class="position-absolute" Style="top: 0; left: 0; right: 0; bottom: 0;" />
    </MudPaper>

    @if (_selectedNode != null)
    {
        <MudPaper Elevation="3" Class="pa-3 mt-3">
            <MudText Typo="Typo.h6">Propriedades do Nó</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField @bind-Value="_selectedNode.Title" Label="Título" Immediate="true" OnBlur="UpdateNode" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedNodeColor" Label="Cor de Fundo" Immediate="true" OnBlur="UpdateNodeColor" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedNodeBorderColor" Label="Cor da Borda" Immediate="true" OnBlur="UpdateNodeBorderColor" />
                </MudItem>
            </MudGrid>
        </MudPaper>
    }

    @if (_selectedLink != null)
    {
        <MudPaper Elevation="3" Class="pa-3 mt-3">
            <MudText Typo="Typo.h6">Propriedades do Link</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudColorPicker @bind-Value="_selectedLinkColor" Label="Cor da Linha" Immediate="true" OnBlur="UpdateLinkColor" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudSlider @bind-Value="_selectedLinkWidth" Min="1" Max="10" Step="1" Label="Espessura da Linha" Immediate="true" ValueLabel="true" OnBlur="UpdateLinkWidth" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudSelect T="string" Label="Marcador" Value="_selectedLinkMarker" ValueChanged="OnLinkMarkerChanged">
                        <MudSelectItem Value="@("none")">Nenhum</MudSelectItem>
                        <MudSelectItem Value="@("arrow")">Seta</MudSelectItem>
                        <MudSelectItem Value="@("circle")">Círculo</MudSelectItem>
                        <MudSelectItem Value="@("square")">Quadrado</MudSelectItem>
                    </MudSelect>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private BlazorDiagram _blazorDiagram;
    private DiagramCanvas _diagramCanvas;
    private string _selectedLinkType = "orthogonal";
    private string _selectedLinkMarker = "arrow";
    private NodeModel _selectedNode;
    private LinkModel _selectedLink;
    private string _selectedNodeColor = "#FFFFFF";
    private string _selectedNodeBorderColor = "#000000";
    private string _selectedLinkColor = "#000000";
    private double _selectedLinkWidth = 2;
    private string _currentDiagramId;

    protected override void OnInitialized()
    {
        var options = new BlazorDiagramOptions
        {
            Links = new BlazorDiagramLinkOptions
            {
                DefaultColor = "#000000",
                DefaultSelectedColor = "#3D8BFD",
                DefaultRouter = new NormalRouter(),
                DefaultPathGenerator = new OrthogonalPathGenerator()
            },
            Zoom = new BlazorDiagramZoomOptions
            {
                Enabled = true,
                Inverse = false,
                ScaleFactor = 1.2
            }
        };

        _blazorDiagram = new BlazorDiagram(options);
        _blazorDiagram.SelectionChanged += OnSelectionChanged;
    }

    private void OnSelectionChanged(IEnumerable<SelectableModel> models)
    {
        _selectedNode = null;
        _selectedLink = null;

        var selectedModel = models.FirstOrDefault();
        if (selectedModel is NodeModel node)
        {
            _selectedNode = node;
            _selectedNodeColor = node.Data?.TryGetValue("BackgroundColor", out var bgColor) == true ? bgColor.ToString() : "#FFFFFF";
            _selectedNodeBorderColor = node.Data?.TryGetValue("BorderColor", out var borderColor) == true ? borderColor.ToString() : "#000000";
        }
        else if (selectedModel is LinkModel link)
        {
            _selectedLink = link;
            _selectedLinkColor = link.Color ?? "#000000";
            _selectedLinkWidth = link.Width;
            _selectedLinkMarker = link.TargetMarker == LinkMarker.Arrow ? "arrow" :
                                link.TargetMarker == LinkMarker.Circle ? "circle" :
                                link.TargetMarker == LinkMarker.Square ? "square" : "none";
        }

        StateHasChanged();
    }

    private void AddNode()
    {
        var node = new NodeModel(new Point(100 + _blazorDiagram.Nodes.Count * 50, 100 + _blazorDiagram.Nodes.Count * 30))
        {
            Title = $"Nó {_blazorDiagram.Nodes.Count + 1}"
        };

        if (node.Data == null)
            node.Data = new Dictionary<string, object>();

        node.Data["BackgroundColor"] = "#FFFFFF";
        node.Data["BorderColor"] = "#000000";

        // Adicionar portas aos quatro lados do nó
        node.AddPort(PortAlignment.Top);
        node.AddPort(PortAlignment.Right);
        node.AddPort(PortAlignment.Bottom);
        node.AddPort(PortAlignment.Left);

        _blazorDiagram.AddNode(node);
    }

    private void UpdateNode()
    {
        if (_selectedNode != null)
        {
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateNodeColor()
    {
        if (_selectedNode != null)
        {
            if (_selectedNode.Data == null)
                _selectedNode.Data = new Dictionary<string, object>();

            _selectedNode.Data["BackgroundColor"] = _selectedNodeColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateNodeBorderColor()
    {
        if (_selectedNode != null)
        {
            if (_selectedNode.Data == null)
                _selectedNode.Data = new Dictionary<string, object>();

            _selectedNode.Data["BorderColor"] = _selectedNodeBorderColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateLinkColor()
    {
        if (_selectedLink != null)
        {
            _selectedLink.Color = _selectedLinkColor;
            _blazorDiagram.Refresh();
        }
    }

    private void UpdateLinkWidth()
    {
        if (_selectedLink != null)
        {
            _selectedLink.Width = _selectedLinkWidth;
            _blazorDiagram.Refresh();
        }
    }

    private void OnLinkTypeChanged(string value)
    {
        _selectedLinkType = value;

        switch (value)
        {
            case "straight":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new StraightPathGenerator();
                break;
            case "smooth":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new SmoothPathGenerator();
                break;
            case "orthogonal":
                _blazorDiagram.Options.Links.DefaultPathGenerator = new OrthogonalPathGenerator();
                break;
        }

        _blazorDiagram.Refresh();
    }

    private void OnLinkMarkerChanged(string value)
    {
        _selectedLinkMarker = value;

        if (_selectedLink != null)
        {
            switch (value)
            {
                case "arrow":
                    _selectedLink.TargetMarker = LinkMarker.Arrow;
                    break;
                case "circle":
                    _selectedLink.TargetMarker = LinkMarker.Circle;
                    break;
                case "square":
                    _selectedLink.TargetMarker = LinkMarker.Square;
                    break;
                default:
                    _selectedLink.TargetMarker = null;
                    break;
            }

            _blazorDiagram.Refresh();
        }
    }

    private async Task SaveDiagram()
    {
        try
        {
            _currentDiagramId = await DiagramPersistenceService.SaveDiagramAsync(_blazorDiagram, "Meu Diagrama", "Diagrama criado com o editor");
            await ShowSnackbar("Diagrama salvo com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            await ShowSnackbar($"Erro ao salvar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDiagram()
    {
        if (string.IsNullOrEmpty(_currentDiagramId))
        {
            await ShowSnackbar("Nenhum diagrama salvo para carregar.", Severity.Warning);
            return;
        }

        try
        {
            var diagram = await DiagramPersistenceService.LoadDiagramAsync(_currentDiagramId);
            if (diagram != null)
            {
                _blazorDiagram.Nodes.Clear();
                _blazorDiagram.Links.Clear();

                foreach (var node in diagram.Nodes)
                {
                    _blazorDiagram.AddNode(node);
                }

                foreach (var link in diagram.Links)
                {
                    _blazorDiagram.AddLink(link);
                }

                await ShowSnackbar("Diagrama carregado com sucesso!", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            await ShowSnackbar($"Erro ao carregar diagrama: {ex.Message}", Severity.Error);
        }
    }

    private async Task ShowSnackbar(string message, Severity severity)
    {
        // Implementar com MudBlazor Snackbar
    }
}
```

### 5.2. Criar CustomNodeWidget.razor

Crie um novo arquivo `Components/CustomNodeWidget.razor` com o seguinte conteúdo:

```razor
<div class="custom-node @(Node.Selected ? "selected" : "")" style="background-color: @GetBackgroundColor(); border-color: @GetBorderColor();">
    <div class="node-title">@(Node.Title ?? "Título")</div>
    @foreach (var port in Node.Ports)
    {
        <PortRenderer @key="port" Port="port" Class="custom-port"></PortRenderer>
    }
</div>

@code {
    [Parameter]
    public NodeModel Node { get; set; } = null!;

    private string GetBackgroundColor()
    {
        return Node.Data?.TryGetValue("BackgroundColor", out var color) == true ? color.ToString() : "#FFFFFF";
    }

    private string GetBorderColor()
    {
        return Node.Data?.TryGetValue("BorderColor", out var color) == true ? color.ToString() : "#000000";
    }
}
```

### 5.3. Adicionar CSS personalizado

Crie ou atualize o arquivo `wwwroot/css/site.css` com o seguinte conteúdo:

```css
.custom-node {
    min-width: 150px;
    min-height: 60px;
    padding: 10px;
    border: 2px solid #000;
    border-radius: 5px;
    background-color: white;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    user-select: none;
}

.custom-node.selected {
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.node-title {
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
}

.custom-port {
    width: 12px;
    height: 12px;
    background-color: #6c757d;
    border-radius: 50%;
    position: absolute;
}

.custom-port:hover {
    background-color: #007bff;
    transform: scale(1.2);
}
```

### 5.4. Registrar o componente personalizado

Adicione o seguinte código ao método `OnInitialized` na classe `DiagramEditorPage.razor`:

```csharp
_blazorDiagram.RegisterComponent<NodeModel, CustomNodeWidget>();
```

## 6. Atualizar NavMenu.razor

Atualize o arquivo `Shared/NavMenu.razor` para incluir um link para o editor de diagramas:

```razor
<MudNavMenu>
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">Home</MudNavLink>
    <MudNavLink Href="diagram-editor" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Edit">Editor de Diagramas</MudNavLink>
    <MudNavLink Href="counter" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Add">Counter</MudNavLink>
    <MudNavLink Href="fetchdata" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.List">Fetch data</MudNavLink>
</MudNavMenu>
```

## 7. Configurar o Banco de Dados

Adicione as seguintes linhas ao arquivo `Program.cs` para configurar o banco de dados:

```csharp
using Microsoft.EntityFrameworkCore;
using Blazor.Diagrams.Persistence;

// Configurar o banco de dados
builder.Services.AddDbContext<DiagramDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DiagramsConnection")));

builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
```

E adicione a string de conexão ao arquivo `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DiagramsConnection": "Data Source=Diagrams.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```