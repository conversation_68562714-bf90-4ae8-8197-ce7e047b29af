# Editor de Diagramas com Blazor Server e MudBlazor

Este documento contém instruções para criar um projeto de editor de diagramas usando Blazor Server e MudBlazor como UI, permitindo criar, salvar e carregar diagramas, inserir nós e interligá-los com links ortogonais, além de editar atributos como cor, espessura das linhas e texto.

## Pré-requisitos

- .NET SDK (recomendado 7.0 ou superior)
- Visual Studio 2022 ou Visual Studio Code
- SQL Server ou SQLite para persistência de dados

## Passo 1: Criar o Projeto Blazor Server

```bash
dotnet new blazorserver -n DiagramEditor
cd DiagramEditor
```

## Passo 2: Adicionar Pacotes NuGet Necessários

```bash
dotnet add package MudBlazor
dotnet add package Microsoft.EntityFrameworkCore.SqlServer
dotnet add package Microsoft.EntityFrameworkCore.Design
dotnet add package Microsoft.EntityFrameworkCore.Tools
```

Adicione também referências aos projetos Blazor.Diagrams:

```bash
dotnet add reference ../Blazor.Diagrams/Blazor.Diagrams.csproj
dotnet add reference ../Blazor.Diagrams.Core/Blazor.Diagrams.Core.csproj
dotnet add reference ../Blazor.Diagrams.Persistence/Blazor.Diagrams.Persistence.csproj
```

## Passo 3: Configurar MudBlazor

### Atualizar _Imports.razor

Adicione as seguintes linhas ao arquivo `_Imports.razor`:

```csharp
@using MudBlazor
@using Blazor.Diagrams
@using Blazor.Diagrams.Core
@using Blazor.Diagrams.Core.Models
@using Blazor.Diagrams.Core.Models.Base
@using Blazor.Diagrams.Components
@using DiagramEditor.Models
@using DiagramEditor.Services
```

### Atualizar Program.cs

Modifique o arquivo `Program.cs` para incluir os serviços do MudBlazor e do Blazor.Diagrams:

```csharp
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using MudBlazor.Services;
using DiagramEditor.Data;
using DiagramEditor.Services;
using Microsoft.EntityFrameworkCore;
using Blazor.Diagrams.Core.Persistence;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddMudServices();

// Adicionar DbContext
builder.Services.AddDbContext<DiagramDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Registrar serviços
builder.Services.AddScoped<IDiagramPersistenceService, DiagramPersistenceService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

app.Run();
```

### Atualizar appsettings.json

Adicione a string de conexão ao banco de dados no arquivo `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=DiagramEditor;Trusted_Connection=True;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```

### Atualizar _Host.cshtml

Modifique o arquivo `Pages/_Host.cshtml` para incluir os estilos do MudBlazor:

```html
@page "/"
@namespace DiagramEditor.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = "_Layout";
}

<component type="typeof(App)" render-mode="ServerPrerendered" />

<script src="_framework/blazor.server.js"></script>
<script src="_content/MudBlazor/MudBlazor.min.js"></script>
```

### Atualizar _Layout.cshtml

Modifique o arquivo `Pages/_Layout.cshtml` para incluir os estilos do MudBlazor:

```html
@using Microsoft.AspNetCore.Components.Web
@namespace DiagramEditor.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="DiagramEditor.styles.css" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="_content/Blazor.Diagrams/style.css" rel="stylesheet" />
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
</body>
</html>
```

### Atualizar MainLayout.razor

Substitua o conteúdo do arquivo `Shared/MainLayout.razor` pelo seguinte:

```html
@inherits LayoutComponentBase

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">Editor de Diagramas</MudText>
        <MudSpacer />
    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="my-4 pt-4">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
}
```

### Atualizar NavMenu.razor

Substitua o conteúdo do arquivo `Shared/NavMenu.razor` pelo seguinte:

```html
<MudNavMenu>
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">Home</MudNavLink>
    <MudNavLink Href="diagram-editor" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Edit">Editor de Diagramas</MudNavLink>
</MudNavMenu>
```

## Passo 4: Criar os Modelos de Dados

Crie uma pasta `Models` e adicione os arquivos de modelo conforme os exemplos fornecidos em `DiagramModels.cs.example`.

## Passo 5: Criar o Contexto do Banco de Dados

Crie uma pasta `Data` e adicione o arquivo `DiagramDbContext.cs` conforme o exemplo fornecido em `DiagramDbContext.cs.example`.

## Passo 6: Criar o Serviço de Persistência

Crie uma pasta `Services` e adicione o arquivo `DiagramPersistenceService.cs` conforme o exemplo fornecido em `DiagramPersistenceService.cs.example`.

## Passo 7: Criar o Componente de Nó Personalizado

Crie um arquivo `CustomNodeWidget.razor` na pasta `Components` com o conteúdo do exemplo fornecido em `CustomNodeWidget.razor.example`.

## Passo 8: Criar a Página do Editor de Diagramas

Crie um arquivo `DiagramEditorPage.razor` na pasta `Pages` com o conteúdo do exemplo fornecido em `DiagramEditorPage.razor.example`.

## Passo 9: Adicionar Estilos CSS

Adicione os estilos CSS ao arquivo `wwwroot/css/site.css` conforme o exemplo fornecido em `site.css.example`.

## Passo 10: Executar Migrações do Banco de Dados

Execute os seguintes comandos para criar o banco de dados:

```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## Passo 11: Executar o Projeto

```bash
dotnet run
```

Acesse o aplicativo em `https://localhost:5001` ou `http://localhost:5000`.

## Funcionalidades Implementadas

1. **Criação de Diagramas**:
   - Adicionar nós ao diagrama
   - Conectar nós com links ortogonais, retos ou suaves
   - Personalizar a aparência dos nós e links

2. **Edição de Propriedades**:
   - Editar título dos nós
   - Alterar cores de fundo e borda dos nós
   - Modificar cor, espessura e marcadores dos links

3. **Persistência**:
   - Salvar diagramas no banco de dados
   - Carregar diagramas salvos

## Extensões Possíveis

1. **Gerenciamento de Múltiplos Diagramas**:
   - Listar todos os diagramas salvos
   - Excluir diagramas
   - Duplicar diagramas existentes

2. **Exportação e Importação**:
   - Exportar diagramas como imagens ou JSON
   - Importar diagramas de arquivos JSON

3. **Tipos de Nós Personalizados**:
   - Criar diferentes tipos de nós com aparências e comportamentos específicos
   - Adicionar validações para conexões entre diferentes tipos de nós

4. **Colaboração em Tempo Real**:
   - Implementar edição colaborativa usando SignalR
   - Mostrar cursores e ações de outros usuários em tempo real

## Solução de Problemas

1. **Erros de Referência**:
   - Verifique se todas as referências de projeto estão corretas
   - Certifique-se de que os caminhos para os projetos Blazor.Diagrams estão corretos

2. **Erros de Banco de Dados**:
   - Verifique a string de conexão no arquivo `appsettings.json`
   - Certifique-se de que o SQL Server está em execução
   - Execute `dotnet ef database update` novamente

3. **Problemas de Renderização**:
   - Verifique se todos os arquivos CSS e JavaScript estão sendo carregados
   - Inspecione o console do navegador para erros JavaScript
   - Limpe o cache do navegador e reinicie o aplicativo