using Blazor.Diagrams.Core.Persistence;
using Blazor.Diagrams.Core.Persistence.Serialization;
using Blazor.Diagrams.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Blazor.Diagrams.Persistence.Extensions;

/// <summary>
/// Extension methods for configuring diagram persistence services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add diagram persistence services with SQLite database
    /// </summary>
    public static IServiceCollection AddDiagramPersistence(this IServiceCollection services, string connectionString)
    {
        return services.AddDiagramPersistence(options => options.UseSqlite(connectionString));
    }

    /// <summary>
    /// Add diagram persistence services with SQL Server database
    /// </summary>
    public static IServiceCollection AddDiagramPersistenceWithSqlServer(this IServiceCollection services, string connectionString)
    {
        return services.AddDiagramPersistence(options => options.UseSqlServer(connectionString));
    }

    /// <summary>
    /// Add diagram persistence services with custom DbContext configuration
    /// </summary>
    public static IServiceCollection AddDiagramPersistence(this IServiceCollection services, Action<DbContextOptionsBuilder> configureDbContext)
    {
        // Register DbContext
        services.AddDbContext<DiagramDbContext>(configureDbContext);

        // Register persistence services
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IModelSerializer, ModelSerializer>();
        services.AddScoped<IDiagramPersistenceService, DiagramPersistenceService>();

        return services;
    }

    /// <summary>
    /// Add diagram persistence services with in-memory database (for testing)
    /// </summary>
    public static IServiceCollection AddDiagramPersistenceInMemory(this IServiceCollection services, string databaseName = "DiagramsTestDb")
    {
        // Note: UseInMemoryDatabase requires Microsoft.EntityFrameworkCore.InMemory package
        // For now, we'll use SQLite in-memory as an alternative
        return services.AddDiagramPersistence(options => options.UseSqlite("Data Source=:memory:"));
    }

    /// <summary>
    /// Ensure the database is created and migrations are applied
    /// </summary>
    public static IServiceProvider EnsureDiagramDatabase(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<DiagramDbContext>();
        context.Database.EnsureCreated();
        return serviceProvider;
    }

    /// <summary>
    /// Apply pending migrations to the diagram database
    /// </summary>
    public static IServiceProvider MigrateDiagramDatabase(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<DiagramDbContext>();
        context.Database.Migrate();
        return serviceProvider;
    }
}
