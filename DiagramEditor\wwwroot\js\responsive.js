// Serviço responsivo para detecção de dispositivos e gestos touch

let dotNetRef = null;
let touchGesturesEnabled = false;
let lastDeviceInfo = null;

// Inicializar o serviço responsivo
export function initialize(dotNetReference) {
    dotNetRef = dotNetReference;
    
    // Configurar listeners para mudanças de orientação e redimensionamento
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    // Detectar mudanças de orientação em dispositivos móveis
    if (screen.orientation) {
        screen.orientation.addEventListener('change', handleOrientationChange);
    }
    
    console.log('Responsive service initialized');
}

// Obter informações do dispositivo
export function getDeviceInfo() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    
    // Detectar tipo de dispositivo
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || width < 600;
    const isTablet = /iPad|Android/i.test(userAgent) && width >= 600 && width < 1280;
    const isDesktop = width >= 1280;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Detectar orientação (0 = Portrait, 1 = Landscape)
    const orientation = width > height ? 1 : 0;
    
    // Determinar tamanho da tela
    const screenSize = getScreenSize(width);
    
    const deviceInfo = {
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        orientation,
        screenSize,
        screenWidth: width,
        screenHeight: height,
        devicePixelRatio: window.devicePixelRatio || 1,
        userAgent,
        platform
    };
    
    lastDeviceInfo = deviceInfo;
    return deviceInfo;
}

// Determinar tamanho da tela baseado na largura
function getScreenSize(width) {
    if (width < 600) return 'ExtraSmall';
    if (width < 960) return 'Small';
    if (width < 1280) return 'Medium';
    if (width < 1920) return 'Large';
    return 'ExtraLarge';
}

// Handler para redimensionamento da janela
function handleResize() {
    const newDeviceInfo = getDeviceInfo();
    
    // Verificar se houve mudança significativa
    if (hasSignificantChange(lastDeviceInfo, newDeviceInfo)) {
        notifyDeviceInfoChanged(newDeviceInfo);
    }
}

// Handler para mudança de orientação
function handleOrientationChange() {
    // Aguardar um pouco para a orientação se estabilizar
    setTimeout(() => {
        const newDeviceInfo = getDeviceInfo();
        notifyDeviceInfoChanged(newDeviceInfo);
    }, 100);
}

// Verificar se houve mudança significativa no dispositivo
function hasSignificantChange(oldInfo, newInfo) {
    if (!oldInfo) return true;
    
    return oldInfo.isMobile !== newInfo.isMobile ||
           oldInfo.isTablet !== newInfo.isTablet ||
           oldInfo.isDesktop !== newInfo.isDesktop ||
           oldInfo.orientation !== newInfo.orientation ||
           oldInfo.screenSize !== newInfo.screenSize;
}

// Notificar o .NET sobre mudanças no dispositivo
function notifyDeviceInfoChanged(deviceInfo) {
    if (dotNetRef) {
        dotNetRef.invokeMethodAsync('OnDeviceInfoChanged', deviceInfo);
    }
}

// Habilitar gestos touch para diagramas
export function enableTouchGestures() {
    if (touchGesturesEnabled) return;
    
    touchGesturesEnabled = true;
    
    // Configurar gestos para o canvas do diagrama
    const diagramCanvas = document.querySelector('.diagram-canvas') || document.querySelector('[data-diagram]');
    if (diagramCanvas) {
        setupTouchGestures(diagramCanvas);
    }
    
    console.log('Touch gestures enabled');
}

// Desabilitar gestos touch
export function disableTouchGestures() {
    touchGesturesEnabled = false;
    
    const diagramCanvas = document.querySelector('.diagram-canvas') || document.querySelector('[data-diagram]');
    if (diagramCanvas) {
        removeTouchGestures(diagramCanvas);
    }
    
    console.log('Touch gestures disabled');
}

// Configurar gestos touch no elemento
function setupTouchGestures(element) {
    let touchState = {
        touches: [],
        lastDistance: 0,
        lastCenter: { x: 0, y: 0 },
        isPanning: false,
        isPinching: false
    };
    
    // Touch start
    element.addEventListener('touchstart', (e) => {
        e.preventDefault();
        touchState.touches = Array.from(e.touches);
        
        if (touchState.touches.length === 1) {
            // Início do pan
            touchState.isPanning = true;
            touchState.lastCenter = getTouchCenter(touchState.touches);
        } else if (touchState.touches.length === 2) {
            // Início do pinch
            touchState.isPanning = false;
            touchState.isPinching = true;
            touchState.lastDistance = getTouchDistance(touchState.touches);
            touchState.lastCenter = getTouchCenter(touchState.touches);
        }
    }, { passive: false });
    
    // Touch move
    element.addEventListener('touchmove', (e) => {
        e.preventDefault();
        touchState.touches = Array.from(e.touches);
        
        if (touchState.isPanning && touchState.touches.length === 1) {
            // Pan gesture
            const currentCenter = getTouchCenter(touchState.touches);
            const deltaX = currentCenter.x - touchState.lastCenter.x;
            const deltaY = currentCenter.y - touchState.lastCenter.y;
            
            // Aplicar pan ao diagrama
            panDiagram(deltaX, deltaY);
            
            touchState.lastCenter = currentCenter;
        } else if (touchState.isPinching && touchState.touches.length === 2) {
            // Pinch gesture
            const currentDistance = getTouchDistance(touchState.touches);
            const currentCenter = getTouchCenter(touchState.touches);
            
            const scale = currentDistance / touchState.lastDistance;
            
            // Aplicar zoom ao diagrama
            zoomDiagram(scale, currentCenter);
            
            touchState.lastDistance = currentDistance;
            touchState.lastCenter = currentCenter;
        }
    }, { passive: false });
    
    // Touch end
    element.addEventListener('touchend', (e) => {
        e.preventDefault();
        touchState.touches = Array.from(e.touches);
        
        if (touchState.touches.length === 0) {
            touchState.isPanning = false;
            touchState.isPinching = false;
        } else if (touchState.touches.length === 1 && touchState.isPinching) {
            // Transição de pinch para pan
            touchState.isPinching = false;
            touchState.isPanning = true;
            touchState.lastCenter = getTouchCenter(touchState.touches);
        }
    }, { passive: false });
}

// Remover gestos touch do elemento
function removeTouchGestures(element) {
    // Clonar o elemento para remover todos os listeners
    const newElement = element.cloneNode(true);
    element.parentNode.replaceChild(newElement, element);
}

// Obter centro dos toques
function getTouchCenter(touches) {
    const x = touches.reduce((sum, touch) => sum + touch.clientX, 0) / touches.length;
    const y = touches.reduce((sum, touch) => sum + touch.clientY, 0) / touches.length;
    return { x, y };
}

// Obter distância entre dois toques
function getTouchDistance(touches) {
    if (touches.length < 2) return 0;
    
    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
}

// Aplicar pan ao diagrama
function panDiagram(deltaX, deltaY) {
    const diagramContainer = document.querySelector('.diagram-canvas');
    if (!diagramContainer) return;
    
    // Obter transformação atual
    const transform = getComputedStyle(diagramContainer).transform;
    let currentX = 0, currentY = 0, currentScale = 1;
    
    if (transform && transform !== 'none') {
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
            const values = matrix[1].split(',').map(parseFloat);
            currentX = values[4] || 0;
            currentY = values[5] || 0;
            currentScale = values[0] || 1;
        }
    }
    
    // Aplicar nova transformação
    const newX = currentX + deltaX;
    const newY = currentY + deltaY;
    
    diagramContainer.style.transform = `translate(${newX}px, ${newY}px) scale(${currentScale})`;
}

// Aplicar zoom ao diagrama
function zoomDiagram(scale, center) {
    const diagramContainer = document.querySelector('.diagram-canvas');
    if (!diagramContainer) return;
    
    // Obter transformação atual
    const transform = getComputedStyle(diagramContainer).transform;
    let currentX = 0, currentY = 0, currentScale = 1;
    
    if (transform && transform !== 'none') {
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
            const values = matrix[1].split(',').map(parseFloat);
            currentX = values[4] || 0;
            currentY = values[5] || 0;
            currentScale = values[0] || 1;
        }
    }
    
    // Calcular nova escala (com limites)
    const newScale = Math.max(0.1, Math.min(5.0, currentScale * scale));
    
    // Calcular ajuste de posição para zoom no centro
    const rect = diagramContainer.getBoundingClientRect();
    const centerX = center.x - rect.left;
    const centerY = center.y - rect.top;
    
    const scaleDiff = newScale - currentScale;
    const newX = currentX - (centerX * scaleDiff);
    const newY = currentY - (centerY * scaleDiff);
    
    // Aplicar nova transformação
    diagramContainer.style.transform = `translate(${newX}px, ${newY}px) scale(${newScale})`;
}

// Adicionar estilos CSS responsivos
function addResponsiveStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Estilos responsivos para dispositivos móveis */
        @media (max-width: 600px) {
            .diagram-toolbar {
                flex-wrap: wrap;
                gap: 4px;
            }
            
            .diagram-toolbar .mud-button {
                min-width: 44px;
                min-height: 44px;
            }
            
            .diagram-canvas {
                touch-action: none;
                user-select: none;
            }
            
            .mud-drawer {
                width: 280px !important;
            }
        }
        
        /* Estilos para tablets */
        @media (min-width: 601px) and (max-width: 1279px) {
            .diagram-toolbar {
                gap: 6px;
            }
            
            .diagram-toolbar .mud-button {
                min-width: 48px;
                min-height: 48px;
            }
            
            .mud-drawer {
                width: 320px !important;
            }
        }
        
        /* Estilos para desktop */
        @media (min-width: 1280px) {
            .diagram-toolbar {
                gap: 8px;
            }
            
            .mud-drawer {
                width: 350px !important;
            }
        }
        
        /* Estilos para dispositivos touch */
        .touch-device .diagram-node {
            cursor: pointer;
        }
        
        .touch-device .diagram-toolbar .mud-button {
            min-width: 44px;
            min-height: 44px;
        }
    `;
    document.head.appendChild(style);
}

// Inicializar estilos responsivos quando o DOM estiver pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addResponsiveStyles);
} else {
    addResponsiveStyles();
}
