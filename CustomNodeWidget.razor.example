<div class="custom-node @(Node.Selected ? "selected" : "")" style="background-color: @GetBackgroundColor(); border-color: @GetBorderColor();">
    <div class="node-title">@(Node.Title ?? "Título")</div>
    @foreach (var port in Node.Ports)
    {
        <PortRenderer @key="port" Port="port" Class="custom-port"></PortRenderer>
    }
</div>

@code {
    [Parameter]
    public NodeModel Node { get; set; } = null!;

    private string GetBackgroundColor()
    {
        return Node.Data?.TryGetValue("BackgroundColor", out var color) == true ? color.ToString() : "#FFFFFF";
    }

    private string GetBorderColor()
    {
        return Node.Data?.TryGetValue("BorderColor", out var color) == true ? color.ToString() : "#000000";
    }
}